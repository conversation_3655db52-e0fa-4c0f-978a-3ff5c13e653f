"""
选择器优先执行模块
实现"选择器优先，Agent备用"的执行策略
"""

import asyncio
from typing import Optional, Any, Dict, List
from playwright.async_api import Page, TimeoutError as PlaywrightTimeoutError
from logger_config import logger
from core.selectors_config import selectors_manager, SelectorConfig
from datetime import datetime, timedelta

class SelectorExecutor:
    """セレクタ優先実行器 - MCP強化版"""
    
    def __init__(self, page: Page):
        self.page = page
        self.success_selectors = {}  # 成功したセレクタを記録
        self.mcp_tools = None  # MCPツール
        
    async def initialize_mcp_fallback(self):
        """MCPバックアップツールを初期化"""
        try:
            from agents.tools.mcp_browser_tools import MCPBrowserTools
            self.mcp_tools = MCPBrowserTools()
            await self.mcp_tools.initialize(self.page)
            logger.info("✅ MCPバックアップツールが初期化されました")
        except Exception as e:
            logger.warning(f"⚠️ MCPバックアップツール初期化失敗: {e}")
            self.mcp_tools = None
    
    async def smart_click(self, workflow: str = "", category: str = "", element: str = "", 
                         target_text: str = "", **kwargs) -> bool:
        """スマートクリック - 三層バックアップ戦略"""
        # 第一層：セレクタ優先（既存ロジック）
        selector_success = await self._try_selectors_click(workflow, category, element, target_text, **kwargs)
        if selector_success:
            return True
        # 第二層：MCPバックアップ
        if self.mcp_tools and target_text:
            logger.info(f"🔄 セレクタ失効、MCPバックアップ試行: {target_text}")
            mcp_success = await self.mcp_tools.smart_click_fallback(target_text, **kwargs)
            if mcp_success:
                logger.info(f"✅ MCPバックアップ成功: {target_text}")
                return True
        # 第三層：CrewAI Agent（呼び出し元で処理）
        logger.warning(f"❌ セレクタとMCPが失敗: {workflow}.{category}.{element}")
        return False
    
    async def _try_selectors_click(self, workflow: str = "", category: str = "", element: str = "", 
                                  target_text: str = "", **kwargs) -> bool:
        """元のセレクタ試行ロジック（変更なし）"""
        selector_config = selectors_manager.get_selector(workflow, category, element)
        if not selector_config:
            logger.warning(f"セレクタ設定が見つかりません: {workflow}.{category}.{element}")
            return False
        
        # すべてのセレクタを試行
        all_selectors = [selector_config.primary] + selector_config.fallbacks
        
        for i, selector in enumerate(all_selectors):
            try:
                logger.info(f"セレクタクリック試行 {i+1}/{len(all_selectors)}: {selector}")
                
                # ターゲットテキストがある場合、テキストセレクタを使用
                if target_text and selector.startswith('text='):
                    actual_selector = f'text={target_text}'
                elif target_text and 'text=' not in selector:
                    # 非テキストセレクタの場合、元のセレクタを先に試行
                    actual_selector = selector
                else:
                    actual_selector = selector
                
                await self.page.click(actual_selector, timeout=selector_config.timeout)

                # 不立即等待networkidle，让调用者决定是否需要等待
                logger.info(f"✅ クリック成功: {selector_config.description} ({actual_selector})")
                
                # 🆕 学习成功的选择器
                selectors_manager.learn_from_success(workflow, category, element, selector)
                
                return True
                
            except Exception as e:
                logger.debug(f"セレクタ失敗 {selector}: {e}")
                continue
        
        logger.warning(f"❌ すべてのセレクタが失敗しました: {selector_config.description}")
        return False
    
    async def smart_fill(self, workflow: str, category: str, element: str, 
                        value: str, clear_first: bool = True) -> bool:
        """智能填充 - 选择器优先"""
        selector_config = selectors_manager.get_selector(workflow, category, element)
        if not selector_config:
            logger.warning(f"选择器配置未找到: {workflow}.{category}.{element}")
            return False
        
        all_selectors = [selector_config.primary] + selector_config.fallbacks
        
        for i, selector in enumerate(all_selectors):
            try:
                logger.info(f"尝试填充选择器 {i+1}/{len(all_selectors)}: {selector}")
                
                if clear_first:
                    await self.page.fill(selector, "", timeout=selector_config.timeout)
                await self.page.fill(selector, value, timeout=selector_config.timeout)
                
                logger.info(f"✅ 填充成功: {selector_config.description} ({selector})")
                
                # 🆕 学习成功的选择器
                selectors_manager.learn_from_success(workflow, category, element, selector)
                
                return True
                
            except Exception as e:
                logger.debug(f"选择器失败 {selector}: {e}")
                continue
        
        logger.warning(f"❌ 所有选择器都失败了: {selector_config.description}")
        return False
    
    async def smart_select_option(self, workflow: str = "", category: str = "", element: str = "", 
                                 value: str = "", text: str = "") -> bool:
        """スマート選択オプション - 三層バックアップ戦略"""
        # 第一層：セレクタ優先
        selector_success = await self._try_selectors_select(workflow, category, element, value, text)
        if selector_success:
            return True
        # 第二層：MCPバックアップ
        if self.mcp_tools and (value or text):
            target_description = text or value
            logger.info(f"🔄 セレクタ失効、MCP選択バックアップ試行: {target_description}")
            mcp_success = await self.mcp_tools.smart_select_fallback(target_description, value=value)
            if mcp_success:
                logger.info(f"✅ MCP選択バックアップ成功: {target_description}")
                return True
        logger.warning(f"❌ セレクタとMCPが失敗: {workflow}.{category}.{element}")
        return False
    
    async def _try_selectors_select(self, workflow: str = "", category: str = "", element: str = "",
                                   value: str = "", text: str = "") -> bool:
        """元のセレクタ選択ロジック（変更なし）"""
        selector_config = selectors_manager.get_selector(workflow, category, element)
        if not selector_config:
            logger.warning(f"セレクタ設定が見つかりません: {workflow}.{category}.{element}")
            return False
        
        all_selectors = [selector_config.primary] + selector_config.fallbacks
        
        for i, selector in enumerate(all_selectors):
            try:
                logger.info(f"セレクタ選択試行 {i+1}/{len(all_selectors)}: {selector}")
                
                if value:
                    await self.page.select_option(selector, value=value, timeout=selector_config.timeout)
                elif text:
                    await self.page.select_option(selector, label=text, timeout=selector_config.timeout)
                else:
                    logger.warning("valueまたはtextパラメータが必要です")
                    continue
                
                logger.info(f"✅ 選択成功: {selector_config.description} ({selector})")
                
                # 成功したセレクタの優先度を更新
                if i > 0:
                    selectors_manager.update_selector_priority(workflow, category, element, selector)
                
                return True
                
            except Exception as e:
                logger.debug(f"セレクタ失敗 {selector}: {e}")
                continue
        
        logger.warning(f"❌ すべてのセレクタが失敗しました: {selector_config.description}")
        return False
    
    async def smart_wait_for_element(self, workflow: str, category: str, element: str) -> bool:
        """智能等待元素 - 选择器优先"""
        selector_config = selectors_manager.get_selector(workflow, category, element)
        if not selector_config:
            return False
        
        all_selectors = [selector_config.primary] + selector_config.fallbacks
        
        for selector in all_selectors:
            try:
                await self.page.wait_for_selector(selector, timeout=selector_config.timeout)
                logger.info(f"✅ 元素出现: {selector_config.description} ({selector})")
                return True
            except Exception as e:
                logger.debug(f"等待元素失败 {selector}: {e}")
                continue
        
        return False
    
    async def execute_kanamic_login(self, username: str, password: str) -> bool:
        """执行Kanamic登录流程 - 基于autoro代码的选择器优先"""
        logger.info("开始Kanamic登录流程（选择器优先）")
        
        try:
            # 1. 填充用户名
            if not await self.smart_fill("kanamic", "login", "username_field", username):
                logger.error("用户名填充失败")
                return False
            
            # 2. 填充密码
            if not await self.smart_fill("kanamic", "login", "password_field", password):
                logger.error("密码填充失败")
                return False
            
            # 3. 点击登录按钮
            if not await self.smart_click("kanamic", "login", "login_button"):
                logger.error("登录按钮点击失败")
                return False
            
            # 4. 等待登录完成
            await self.page.wait_for_load_state('networkidle', timeout=30000)
            logger.info("✅ Kanamic登录成功")
            return True
            
        except Exception as e:
            logger.error(f"Kanamic登录过程中发生错误: {e}")
            return False
    
    async def execute_kanamic_csv_download(self, download_path: str = "", drive_client = None, gdrive_folder_id: str = "") -> bool:
        """Kanamic CSVダウンロードフローを実行 - MCP強化版（ファイル処理含む）"""
        logger.info("Kanamic CSVダウンロードフローを開始（MCP強化版）")
        
        try:
            # 1. 28号メニューリンクをクリック（セレクタ優先 + MCPバックアップ）
            if not await self.smart_click("kanamic", "csv_download", "menu_link_28"):
                logger.error("28号メニューリンククリック失敗")
                return False
            
            # 2. Button-Bをクリック（セレクタ優先 + MCPバックアップ）
            if not await self.smart_click("kanamic", "csv_download", "button_b", target_text="LIFE用データ出力"):
                logger.error("Button-Bクリック失敗")
                return False
            
            # 3. サービスタイプを選択（地域密着型介護福祉施設入所者生活介護）
            if not await self.smart_select_option("kanamic", "csv_download", "service_type_select", value="54"):
                logger.error("サービスタイプ選択失敗")
                return False
            
            # 4. 現在の月を選択（JavaScriptを使用）
            if not await self.execute_current_month_selection():
                logger.error("月選択失敗")
                return False
            
            # 5. 検索ボタンをクリック（.mb5 span）
            if not await self.smart_click("kanamic", "csv_download", "checkbox_mb5"):
                logger.error("検索ボタンクリック失敗")
                return False
            logger.info("検索ボタンをクリックしました。")
            
            # 検索結果の読み込みを待機
            await self.page.wait_for_load_state('networkidle', timeout=30000)
            await self.page.wait_for_timeout(3000)  # 追加で3秒待機
            logger.info("検索結果の読み込み完了を待機しました。")
            
            # 6. 結果の選択（.rowB p:nth-of-type(2) span）
            if not await self.smart_click("kanamic", "csv_download", "checkbox_rowb"):
                logger.error("結果選択失敗")
                return False
            logger.info("結果を選択しました。")
            
            # 7. ダウンロードボタンをクリック（#predlbtn）- ダウンロード待機付き
            try:
                # ダウンロード待機を設定
                async with self.page.expect_download(timeout=30000) as download_info:
                    if not await self.smart_click("kanamic", "csv_download", "download_button", target_text="確認用CSV"):
                        logger.error("ダウンロードボタンクリック失敗")
                        return False
                    logger.info("ダウンロードボタンをクリックしました。")
                
                # ダウンロード完了を待機
                download = await download_info.value
                logger.info(f"✅ ダウンロード開始確認: {download.suggested_filename}")
                
                # ファイルを指定パスに保存
                import os
                os.makedirs(download_path, exist_ok=True)
                download_file_path = os.path.join(download_path, download.suggested_filename)
                await download.save_as(download_file_path)
                logger.info(f"✅ ファイル保存完了: {download_file_path}")
                
            except Exception as e:
                logger.warning(f"⚠️ ダウンロード待機でエラー: {e}")
                logger.info("従来の待機方法にフォールバック")
                
                # フォールバック: 通常のクリック
                if not await self.smart_click("kanamic", "csv_download", "download_button", target_text="確認用CSV"):
                    logger.error("ダウンロードボタンクリック失敗")
                    return False
                logger.info("ダウンロードボタンをクリックしました。")
            

            # 9. ダウンロードしたファイルを処理（Google Driveアップロード）
            if download_path and drive_client and gdrive_folder_id:
                logger.info("ファイル処理を開始します...")
                await self.process_downloaded_files(download_path, drive_client, gdrive_folder_id)
            else:
                logger.warning(f"ファイル処理がスキップされました - download_path: {download_path}, drive_client: {drive_client is not None}, gdrive_folder_id: {gdrive_folder_id}")

            logger.info("✅ Kanamic CSVダウンロードフロー完了（MCP強化版）")
            return True
            
        except Exception as e:
            logger.error(f"Kanamic CSVダウンロード処理中にエラーが発生: {e}")
            return False
    
    async def execute_current_month_selection(self) -> bool:
        """执行当前月份选择 - 强制选择当前月份"""
        try:
            # 使用JavaScript代码强制选择当前月份
            js_code = """
            (function() {
                var today = new Date();
                var currentMonth = today.getMonth() + 1;  // 当前月份
                var selectElement = document.querySelector("#targetMonth");
                if (!selectElement) {
                    document.body.setAttribute("data-selection-result", "未找到选择元素");
                    return false;
                }
                
                var options = selectElement.options;
                var selectedIndex = -1;
                
                // 强制选择当前月份
                var monthStr = currentMonth < 10 ? "0" + currentMonth : currentMonth.toString();
                var monthStrNoPad = currentMonth.toString();
                
                console.log("正在查找当前月份:", currentMonth, "格式:", monthStr, monthStrNoPad);
                
                // 列出所有可用选项用于调试
                console.log("可用的月份选项:");
                for (var i = 0; i < options.length; i++) {
                    console.log("选项", i, ":", options[i].value, "-", options[i].text);
                }
                
                // 查找当前月份
                for (var i = 0; i < options.length; i++) {
                    var val = options[i].value;
                    var txt = options[i].text;
                    if (val === monthStr || val === monthStrNoPad || txt.indexOf(monthStrNoPad + "月") !== -1) {
                        selectedIndex = i;
                        console.log("找到当前月份选项:", i, val, txt);
                        break;
                    }
                }

                if (selectedIndex !== -1) {
                    // 强制选择并确保不会被系统改变
                    selectElement.selectedIndex = selectedIndex;
                    
                    // 触发change事件
                    var evt = new Event("change", { bubbles: true });
                    selectElement.dispatchEvent(evt);
                    
                    // 等待一下，然后再次确认选择
                    setTimeout(function() {
                        if (selectElement.selectedIndex !== selectedIndex) {
                            console.log("检测到选择被系统改变，重新设置为当前月份");
                            selectElement.selectedIndex = selectedIndex;
                            selectElement.dispatchEvent(new Event("change", { bubbles: true }));
                        }
                    }, 500);
                    
                    document.body.setAttribute("data-selection-result", "强制选择当前月: " + options[selectedIndex].text);
                    console.log("强制选择当前月:", options[selectedIndex].text, options[selectedIndex].value);
                    
                    return {
                        success: true,
                        selectedMonth: options[selectedIndex].text,
                        selectedValue: options[selectedIndex].value,
                        selectedIndex: selectedIndex
                    };
                } else {
                    console.warn("未找到当前月份选项，当前月份:", currentMonth);
                    document.body.setAttribute("data-selection-result", "未找到当前月份: " + currentMonth);
                    return false;
                }
            })()
            """
            
            result = await self.page.evaluate(js_code)
            if result and result.get('success'):
                logger.info(f"✅ 强制选择当前月份成功: {result.get('selectedMonth')} (值: {result.get('selectedValue')})")
                
                # 等待一下，然后再次确认选择没有被改变
                await self.page.wait_for_timeout(1000)
                
                # 验证选择是否保持
                verify_js = """
                (function() {
                    var selectElement = document.querySelector("#targetMonth");
                    if (selectElement) {
                        var currentSelection = selectElement.options[selectElement.selectedIndex];
                        return {
                            selectedText: currentSelection.text,
                            selectedValue: currentSelection.value,
                            selectedIndex: selectElement.selectedIndex
                        };
                    }
                    return null;
                })()
                """
                
                verification = await self.page.evaluate(verify_js)
                if verification:
                    logger.info(f"验证月份选择: {verification.get('selectedText')} (值: {verification.get('selectedValue')})")
                    
                    # 如果选择被改变了，再次强制设置
                    if verification.get('selectedIndex') != result.get('selectedIndex'):
                        logger.warning("检测到月份选择被系统改变，重新强制设置")
                        force_js = f"""
                        (function() {{
                            var selectElement = document.querySelector("#targetMonth");
                            if (selectElement) {{
                                selectElement.selectedIndex = {result.get('selectedIndex')};
                                selectElement.dispatchEvent(new Event("change", {{ bubbles: true }}));
                                return true;
                            }}
                            return false;
                        }})()
                        """
                        await self.page.evaluate(force_js)
                        logger.info("重新强制设置当前月份完成")
                
                return True
            else:
                logger.warning("强制选择当前月份失败，尝试备用方法")
                # 备用方法：直接使用选择器选择当前月
                today = datetime.now()
                month_value = f"{today.month:02d}"
                return await self.smart_select_option("kanamic", "csv_download", "target_month_select", value=month_value)
                
        except Exception as e:
            logger.error(f"月份选择过程中发生错误: {e}")
            return False

    async def execute_download_with_wait(self) -> bool:
        """执行下载并等待下载完成"""
        try:
            # 点击下载按钮并等待下载
            async with self.page.expect_download() as download_info:
                # 点击下载按钮
                if not await self.smart_click("kanamic", "csv_download", "download_button"):
                    logger.error("下载按钮点击失败")
                    return False

            # 获取下载对象
            download = await download_info.value
            logger.info(f"开始下载文件: {download.suggested_filename}")

            # 保存下载的文件到指定目录
            download_path = "/tmp/kanamic_csv_downloads"
            import os
            os.makedirs(download_path, exist_ok=True)

            download_file_path = os.path.join(download_path, download.suggested_filename)
            await download.save_as(download_file_path)
            logger.info(f"文件下载完成: {download_file_path}")

            return True

        except Exception as e:
            logger.error(f"下载过程中发生错误: {e}")
            # 如果下载失败，尝试简单点击下载按钮
            logger.info("尝试简单点击下载按钮...")
            return await self.smart_click("kanamic", "csv_download", "download_button")
    
    async def execute_kaipoke_login(self, corporation_id: str, member_login_id: str, password: str) -> bool:
        """执行Kaipoke登录流程 - 基于RPA平台代码的精确选择器，处理错误页面"""
        logger.info("🔐 开始Kaipoke登录流程（基于RPA平台代码的精确选择器）")

        try:
            # 0. 首先处理错误页面 - RPA代码中的第一步
            logger.info("🔧 检查是否在错误页面，需要点击继续按钮")
            try:
                # 等待页面加载
                await self.page.wait_for_timeout(1000)

                # 检查是否有错误页面的继续按钮
                continue_button = await self.page.locator('.box-btn a').count()
                if continue_button > 0:
                    logger.info("📋 发现错误页面，点击继续按钮")
                    await self.page.click('.box-btn a', timeout=30000)
                    await self.page.wait_for_timeout(1000)  # RPA代码中的等待时间
                    logger.info("✅ 错误页面继续按钮点击完成")
                else:
                    logger.info("ℹ️ 没有发现错误页面，直接进行登录")
            except Exception as e:
                logger.warning(f"⚠️ 错误页面处理失败，继续登录流程: {e}")

            # 1. 填写法人ID - 使用RPA代码中的精确选择器
            logger.info("📝 填写法人ID")
            if not await self.smart_fill("kaipoke", "login", "corporation_id", corporation_id):
                logger.error("❌ 法人ID输入失败")
                return False
            logger.info("✅ 法人ID已填写")
            await self.page.wait_for_timeout(1000)  # RPA代码中的等待时间

            # 2. 填写成员ID - 使用RPA代码中的精确选择器
            logger.info("📝 填写成员ID")
            if not await self.smart_fill("kaipoke", "login", "member_login_id", member_login_id):
                logger.error("❌ 成员ID输入失败")
                return False
            logger.info("✅ 成员ID已填写")
            await self.page.wait_for_timeout(1000)  # RPA代码中的等待时间

            # 3. 填写密码 - 使用RPA代码中的精确选择器
            logger.info("🔒 填写密码")
            if not await self.smart_fill("kaipoke", "login", "password", password):
                logger.error("❌ 密码输入失败")
                return False
            logger.info("✅ 密码已填写")
            await self.page.wait_for_timeout(1000)  # RPA代码中的等待时间

            # 4. 点击登录按钮 - 完全按照RPA代码的方式
            logger.info("🚀 点击登录按钮（完全按照RPA代码方式）...")
            try:
                # 直接使用RPA代码中的选择器，设置30秒超时
                await self.page.click('#form\\:logn_nochklogin', timeout=30000)
                logger.info("✅ 登录按钮点击成功")

                # 等待1秒，与RPA代码完全一致
                await self.page.wait_for_timeout(1000)
                logger.info("✅ 等待1秒完成（与RPA代码一致）")

            except Exception as e:
                logger.error(f"❌ 登录按钮点击失败: {e}")
                return False

            # 5. 检查登录结果
            logger.info(f"📄 登录后页面URL: {self.page.url}")
            logger.info(f"📄 登录后页面标题: {await self.page.title()}")

            # 6. 检查是否进入主页面（检查レセプト菜单是否存在）
            try:
                await self.page.wait_for_selector('.mainCtg li:nth-of-type(1) a', timeout=10000)
                logger.info("🎉 登录成功，主菜单已出现")
                return True
            except Exception as e:
                logger.warning(f"⚠️ 登录后主菜单未出现，可能需要更多时间: {e}")
                # 再等待一下
                await self.page.wait_for_timeout(3000)
                try:
                    await self.page.wait_for_selector('.mainCtg li:nth-of-type(1) a', timeout=5000)
                    logger.info("🎉 登录成功，主菜单已出现（延迟加载）")
                    return True
                except:
                    logger.error("❌ 登录失败，主菜单未出现")
                    return False

        except Exception as e:
            logger.error(f"❌ Kaipoke登录过程中发生错误: {e}")
            return False

    async def smart_get_text(self, platform: str, category: str, selector_key: str) -> str:
        """智能获取文本内容"""
        logger.info(f"尝试获取文本: {platform}.{category}.{selector_key}")

        try:
            selector_config = selectors_manager.get_selector(platform, category, selector_key)
            if not selector_config:
                logger.warning(f"❌ 选择器配置未找到: {platform}.{category}.{selector_key}")
                return ""

            # 尝试主要选择器
            try:
                element = self.page.locator(selector_config.primary).first
                text = await element.inner_text()
                logger.info(f"✅ 文本获取成功: {selector_config.description} ({selector_config.primary})")
                return text.strip()
            except Exception as e:
                logger.debug(f"主要选择器失败 {selector_config.primary}: {e}")

            # 尝试备用选择器
            for i, fallback_selector in enumerate(selector_config.fallbacks, 1):
                try:
                    logger.info(f"尝试备用选择器 {i}/{len(selector_config.fallbacks)}: {fallback_selector}")
                    element = self.page.locator(fallback_selector).first
                    text = await element.inner_text()
                    logger.info(f"✅ 文本获取成功（备用选择器）: {fallback_selector}")
                    return text.strip()
                except Exception as e:
                    logger.debug(f"备用选择器失败 {fallback_selector}: {e}")

            logger.warning(f"❌ 所有选择器都失败了: {platform}.{category}.{selector_key}")
            return ""

        except Exception as e:
            logger.error(f"❌ 文本获取过程中发生错误: {e}")
            return ""

    async def select_previous_month_kaipoke(self) -> bool:
        """选择上个月 - 完全使用与RPA平台一致的JavaScript逻辑"""
        try:
            logger.info("🗓️ 开始选择上个月（完全基于RPA平台代码的JavaScript）")

            # 执行与RPA代码完全一致的JavaScript - inject_script_3的代码，包装为函数
            js_code = """
(function() {
  var selectElement = document.querySelector("#form\\\\:serviceOfferYm");
  if (!selectElement) {
    throw new Error("選択要素が見つかりません");
  }

  var today = new Date();
  var lastMonth = new Date(today);
  lastMonth.setMonth(today.getMonth() - 1);

  var year = lastMonth.getFullYear();
  var month = lastMonth.getMonth() + 1;
  var monthStr = month < 10 ? "0" + month : month.toString();
  var targetValue = year.toString() + monthStr;

  var matchedOption = Array.from(selectElement.options).find(opt =>
    opt.value.includes(targetValue)
  );
  if (!matchedOption) {
    throw new Error("一致する年月の選択肢が見つかりません: " + targetValue);
  }

  selectElement.value = matchedOption.value;
  selectElement.dispatchEvent(new Event("change", { bubbles: true }));

  // 将年月文字设为 HTML 属性
  document.body.setAttribute("data-month-name", matchedOption.text);

  // 可选：也设置 JS 返回值（调试用）
  window.result = {
    text: matchedOption.text,
    value: matchedOption.value
  };

  // 返回结果
  return {
    text: matchedOption.text,
    value: matchedOption.value
  };
})()
"""
            result = await self.page.evaluate(js_code)
            logger.info(f"✅ 上个月选择成功（RPA代码方式）: {result['text']} (值: {result['value']})")
            return True

        except Exception as e:
            logger.error(f"❌ 上个月选择失败: {e}")
            return False
    
    async def execute_kaipoke_monthly_report_download(self, element_text: str, output_filename_pattern: str,
                                                    service_center_name: str, download_path: str) -> Optional[str]:
        """执行Kaipoke月次报告下载流程 - 完全基于RPA平台代码的流程"""
        logger.info(f"🚀 开始Kaipoke月次报告下载（基于RPA平台代码）: {service_center_name}")

        try:
            # 1. 点击レセプト菜单 - 与RPA代码步骤2一致
            logger.info("📋 点击レセプト菜单")
            if not await self.smart_click("kaipoke", "monthly_report", "receipt_menu"):
                logger.error("❌ レセプトメニューのクリックに失敗")
                return None

            # 等待3秒，与RPA代码一致
            await self.page.wait_for_timeout(3000)
            logger.info("✅ レセプトメニューをクリックしました（3秒待機）")

            # 2. 查找并选择据点 - 与RPA代码步骤3一致
            logger.info(f"🏢 查找据点: {element_text}")
            try:
                # 首先等待元素出现
                await self.page.wait_for_selector(f'//a[contains(text(), "{element_text}")]', timeout=5000)
                logger.info(f"✅ 找到据点元素: {element_text}")
            except Exception as e:
                logger.error(f"❌ 据点元素未找到: {element_text}, {e}")
                return None

            # 3. 点击据点 - 与RPA代码步骤4一致
            try:
                await self.page.click(f'//a[contains(text(), "{element_text}")]', timeout=5000)
                # 等待20秒，与RPA代码一致
                await self.page.wait_for_timeout(20000)
                logger.info(f"✅ 据点选择完成: {element_text}（20秒待機）")
            except Exception as e:
                logger.error(f"❌ 据点点击失败: {element_text}, {e}")
                return None

            # 4. マウスオーバー（各種情報出力）- 与RPA代码步骤5一致
            logger.info("🖱️ マウスオーバー（各種情報出力）")
            try:
                if not await self.smart_click("kaipoke", "monthly_report", "info_output_hover"):
                    # 如果smart_click失败，尝试直接hover
                    await self.page.hover('li:nth-of-type(7) img')
                logger.info("✅ 各種情報出力にマウスオーバー完了")
            except Exception as e:
                logger.error(f"❌ 各種情報出力マウスオーバー失败: {e}")
                return None

            # 5. クリック（出力対象選択）- 与RPA代码步骤6一致
            logger.info("📤 クリック（出力対象選択）")
            try:
                if not await self.smart_click("kaipoke", "monthly_report", "output_target_selection"):
                    logger.error("❌ 出力対象選択のクリックに失敗")
                    return None
                # 等待1秒，与RPA代码一致
                await self.page.wait_for_timeout(1000)
                logger.info("✅ 出力対象選択をクリックしました（1秒待機）")
            except Exception as e:
                logger.error(f"❌ 出力対象選択クリック失败: {e}")
                return None

            # 6. クリック（月次実績チップ）- 与RPA代码步骤7一致
            logger.info("📊 クリック（月次実績チップ）")
            try:
                if not await self.smart_click("kaipoke", "monthly_report", "monthly_result_tip"):
                    logger.error("❌ 月次実績チップのクリックに失敗")
                    return None
                logger.info("✅ 月次実績チップをクリックしました")
            except Exception as e:
                logger.error(f"❌ 月次実績チップクリック失败: {e}")
                return None

            # 7. 获取上个月信息并选择 - 与RPA代码步骤8-10一致
            logger.info("📅 获取上个月信息并选择")
            try:
                # 等待页面加载，与RPA代码一致
                await self.page.wait_for_timeout(3000)

                if not await self.select_previous_month_kaipoke():
                    logger.error("❌ 前月選択に失敗")
                    return None
                logger.info("✅ 前月選択完了")
            except Exception as e:
                logger.error(f"❌ 前月選択に失敗: {e}")
                return None

            # 8. 実績区分を選択 - 与RPA代码步骤11一致
            logger.info("📋 実績区分を選択")
            try:
                if not await self.smart_click("kaipoke", "monthly_report", "result_division"):
                    logger.error("❌ 実績区分の選択に失敗")
                    return None
                logger.info("✅ 実績区分を選択しました")
            except Exception as e:
                logger.error(f"❌ 実績区分の選択に失敗: {e}")
                return None

            # 9. ダウンロード - 与RPA代码步骤12一致
            logger.info("💾 Excel出力ボタンをクリックしてダウンロード")
            try:
                # 等待3秒，与RPA代码一致
                await self.page.wait_for_timeout(3000)
                logger.info("ダウンロード前に3秒待機完了")

                # ダウンロード待機を設定
                async with self.page.expect_download(timeout=30000) as download_info:
                    if not await self.smart_click("kaipoke", "monthly_report", "excel_export_button"):
                        logger.error("❌ Excel出力ボタンのクリックに失敗")
                        return None
                    logger.info("✅ Excel出力ボタンをクリックしました")

                # ダウンロード完了を待機
                download = await download_info.value
                logger.info(f"📥 ダウンロード開始: {download.suggested_filename}")

                # ダウンロード完了を待つ - 与RPA代码步骤13-14一致
                await self.page.wait_for_timeout(3000)
                logger.info("ダウンロード完了を3秒待機")

                # ファイル名を生成 - 与RPA代码步骤15-16一致
                today = datetime.now()
                last_month = today.replace(day=1) - timedelta(days=1)

                # 令和年の計算
                reiwa_year = last_month.year - 2018  # 2019年が令和元年
                month_str = f"令和{reiwa_year}年{last_month.month:02d}月"

                if output_filename_pattern:
                    filename = output_filename_pattern.format(month=month_str)
                else:
                    filename = f"{month_str}{service_center_name}.xlsx"

                import os
                save_path = os.path.join(download_path, filename)
                await download.save_as(save_path)

                logger.info(f"✅ Excel出力完了 - ファイル保存: {save_path}")

                # 10. 据点リストページに戻る - 确保下一个据点可以正常处理
                logger.info("🔄 据点リストページに戻る")
                try:
                    # 使用与RPA代码一致的方式返回据点列表
                    await self.page.goto("https://r.kaipoke.biz/kaipokebiz/common/COM020101.do", wait_until='networkidle', timeout=60000)
                    logger.info("✅ 据点リストページに戻りました")
                except Exception as e:
                    logger.warning(f"⚠️ 据点リストページへの戻りでタイムアウト: {e}")
                    # 即使超时也继续处理，因为文件已经下载完成
                    logger.info("タイムアウトしましたが、ファイルダウンロードは完了しているため処理を続行します")

                return save_path

            except Exception as e:
                logger.error(f"❌ Excel出力ボタンのクリックに失敗: {e}")
                return None

        except Exception as e:
            logger.error(f"Kaipoke月次报告下载过程中发生错误: {e}")
            return None
    
    async def select_previous_month(self) -> bool:
        """选择前月"""
        try:
            today = datetime.now()
            last_month = today.replace(day=1) - timedelta(days=1)
            month_value = f"{last_month.month:02d}"
            
            return await self.smart_select_option("kaipoke", "monthly_report", "target_month_select", value=month_value)
            
        except Exception as e:
            logger.error(f"前月选择失败: {e}")
            return False
    
    async def download_excel_file(self, output_filename_pattern: str, service_center_name: str, download_path: str) -> Optional[str]:
        """下载Excel文件"""
        try:
            # 等待下载
            async with self.page.expect_download(timeout=30000) as download_info:
                if not await self.smart_click("kaipoke", "monthly_report", "excel_export_button"):
                    logger.error("Excel导出按钮点击失败")
                    return None
            
            download = await download_info.value
            
            # 生成文件名
            today = datetime.now()
            last_month = today.replace(day=1) - timedelta(days=1)
            month_str = f"{last_month.year}年{last_month.month:02d}月"
            
            if output_filename_pattern:
                filename = output_filename_pattern.format(month=month_str)
            else:
                filename = f"{month_str}_{service_center_name}.xlsx"
            
            import os
            save_path = os.path.join(download_path, filename)
            await download.save_as(save_path)
            
            logger.info(f"✅ Excel文件下载成功: {save_path}")
            return save_path
            
        except Exception as e:
            logger.error(f"Excel文件下载失败: {e}")
            return None
    
    async def process_downloaded_files(self, download_path: str, drive_client, gdrive_folder_id: str):
        """ダウンロードしたZIPファイルを展開してGoogle Driveにアップロードする"""
        try:
            import os
            import zipfile
            import shutil
            from pathlib import Path
            
            logger.info(f"📁 ファイル処理開始 - ダウンロードパス: {download_path}")
            
            # ダウンロードディレクトリ内のすべてのファイルをチェック
            if not os.path.exists(download_path):
                logger.error(f"ダウンロードパスが存在しません: {download_path}")
                return
            
            all_files = list(Path(download_path).glob('*'))
            logger.info(f"ダウンロードディレクトリ内のファイル数: {len(all_files)}")
            for file in all_files:
                logger.info(f"  - {file.name} ({file.suffix})")
            
            # ZIPファイルを探す
            zip_files = list(Path(download_path).glob('*.zip'))
            if not zip_files:
                logger.warning("❌ ZIPファイルが見つかりません。")
                # 他の形式のファイルもチェック
                other_files = [f for f in all_files if f.is_file()]
                if other_files:
                    logger.info("他のファイル形式が見つかりました:")
                    for file in other_files:
                        logger.info(f"  - {file.name} (サイズ: {file.stat().st_size} bytes)")
                return
            
            latest_zip = max(zip_files, key=os.path.getctime)
            logger.info(f"📦 処理するZIPファイル: {latest_zip.name} (サイズ: {latest_zip.stat().st_size} bytes)")
            
            # ZIPファイルを展開
            extract_path = os.path.join(download_path, 'extracted')
            os.makedirs(extract_path, exist_ok=True)
            
            logger.info("📂 ZIPファイルを展開中...")
            with zipfile.ZipFile(latest_zip, 'r') as zip_ref:
                zip_ref.extractall(extract_path)
                extracted_files = zip_ref.namelist()
                logger.info(f"展開されたファイル数: {len(extracted_files)}")
                for file in extracted_files:
                    logger.info(f"  - {file}")
            
            # 展開されたCSVファイルをGoogle Driveにアップロード
            csv_files = list(Path(extract_path).glob('*.csv'))
            logger.info(f"📊 CSVファイル数: {len(csv_files)}")
            
            if not csv_files:
                logger.warning("❌ 展開されたCSVファイルが見つかりません。")
                # 展開されたすべてのファイルをチェック
                all_extracted = list(Path(extract_path).glob('*'))
                logger.info("展開されたすべてのファイル:")
                for file in all_extracted:
                    logger.info(f"  - {file.name} ({file.suffix})")
                return
            
            upload_success_count = 0
            for csv_file in csv_files:
                try:
                    logger.info(f"☁️ Google Driveにアップロード中: {csv_file.name}")
                    file_id = drive_client.upload_file(
                        file_path=str(csv_file),
                        folder_id=gdrive_folder_id
                    )
                    logger.info(f"✅ CSVファイルをGoogle Driveにアップロードしました: {csv_file.name} (ID: {file_id})")
                    upload_success_count += 1
                except Exception as e:
                    logger.error(f"❌ CSVファイルアップロードでエラーが発生しました {csv_file.name}: {e}")
            
            logger.info(f"📈 アップロード完了: {upload_success_count}/{len(csv_files)} ファイル")
            
            # 一時ファイルを削除
            logger.info("🗑️ 一時ファイルを削除中...")
            os.remove(latest_zip)
            shutil.rmtree(extract_path)
            logger.info("✅ 一時ファイルを削除しました。")

        except Exception as e:
            logger.error(f"❌ ファイル処理でエラーが発生しました: {e}", exc_info=True)

    # 🆕 カイポケ提供日別実績登録ワークフロー用の拡張メソッド
    async def smart_select_option_by_text(self, workflow: str, category: str, element: str, text: str) -> bool:
        """
        テキストによるスマート選択オプション - 選択器優先 + MCP備用

        Args:
            workflow: ワークフロー名
            category: カテゴリ名
            element: 要素名
            text: 選択するテキスト

        Returns:
            bool: 成功したかどうか
        """
        return await self.smart_select_option(workflow, category, element, text=text)

    async def smart_hover(self, workflow: str, category: str, element: str) -> bool:
        """
        スマートホバー - 選択器優先 + MCP備用

        Args:
            workflow: ワークフロー名
            category: カテゴリ名
            element: 要素名

        Returns:
            bool: 成功したかどうか
        """
        # 第一層：セレクタ優先
        selector_config = selectors_manager.get_selector(workflow, category, element)
        if not selector_config:
            logger.warning(f"セレクタ設定が見つかりません: {workflow}.{category}.{element}")
            return False

        all_selectors = [selector_config.primary] + selector_config.fallbacks

        for i, selector in enumerate(all_selectors):
            try:
                logger.info(f"ホバー試行 {i+1}/{len(all_selectors)}: {selector}")
                await self.page.hover(selector, timeout=selector_config.timeout)
                logger.info(f"✅ ホバー成功: {selector_config.description} ({selector})")

                # 成功したセレクタの優先度を更新
                if i > 0:
                    selectors_manager.update_selector_priority(workflow, category, element, selector)

                return True

            except Exception as e:
                logger.debug(f"ホバー失敗 {selector}: {e}")
                continue

        # 第二層：MCPバックアップ
        if self.mcp_tools:
            logger.info(f"🔄 セレクタ失効、MCPホバーバックアップ試行: {selector_config.description}")
            try:
                mcp_success = await self.mcp_tools.smart_hover_fallback(selector_config.description)
                if mcp_success:
                    logger.info(f"✅ MCPホバーバックアップ成功: {selector_config.description}")
                    return True
            except Exception as e:
                logger.error(f"❌ MCPホバーも失敗: {e}")

        logger.warning(f"❌ すべてのセレクタとMCPが失敗: {selector_config.description}")
        return False

    async def smart_get_text(self, workflow: str, category: str, element: str) -> str:
        """
        スマートテキスト取得 - 選択器優先 + MCP備用

        Args:
            workflow: ワークフロー名
            category: カテゴリ名
            element: 要素名

        Returns:
            str: 取得したテキスト（失敗時は空文字列）
        """
        # 第一層：セレクタ優先
        selector_config = selectors_manager.get_selector(workflow, category, element)
        if not selector_config:
            logger.warning(f"セレクタ設定が見つかりません: {workflow}.{category}.{element}")
            return ""

        all_selectors = [selector_config.primary] + selector_config.fallbacks

        for i, selector in enumerate(all_selectors):
            try:
                logger.info(f"テキスト取得試行 {i+1}/{len(all_selectors)}: {selector}")
                text = await self.page.text_content(selector, timeout=selector_config.timeout)
                if text:
                    logger.info(f"✅ テキスト取得成功: {selector_config.description} ({selector}) = '{text}'")

                    # 成功したセレクタの優先度を更新
                    if i > 0:
                        selectors_manager.update_selector_priority(workflow, category, element, selector)

                    return text.strip()

            except Exception as e:
                logger.debug(f"テキスト取得失敗 {selector}: {e}")
                continue

        # 第二層：MCPバックアップ
        if self.mcp_tools:
            logger.info(f"🔄 セレクタ失効、MCPテキスト取得バックアップ試行: {selector_config.description}")
            try:
                mcp_text = await self.mcp_tools.smart_get_text_fallback(selector_config.description)
                if mcp_text:
                    logger.info(f"✅ MCPテキスト取得バックアップ成功: {selector_config.description} = '{mcp_text}'")
                    return mcp_text
            except Exception as e:
                logger.error(f"❌ MCPテキスト取得も失敗: {e}")

        logger.warning(f"❌ すべてのセレクタとMCPが失敗: {selector_config.description}")
        return ""

    async def smart_extract_table_data(self, workflow: str, category: str, element: str) -> List[List[str]]:
        """
        スマートテーブルデータ抽出 - 選択器優先 + MCP備用

        Args:
            workflow: ワークフロー名
            category: カテゴリ名
            element: 要素名

        Returns:
            List[List[str]]: 抽出したテーブルデータ（失敗時は空リスト）
        """
        # 第一層：セレクタ優先
        selector_config = selectors_manager.get_selector(workflow, category, element)
        if not selector_config:
            logger.warning(f"セレクタ設定が見つかりません: {workflow}.{category}.{element}")
            return []

        all_selectors = [selector_config.primary] + selector_config.fallbacks

        for i, selector in enumerate(all_selectors):
            try:
                logger.info(f"テーブルデータ抽出試行 {i+1}/{len(all_selectors)}: {selector}")

                # テーブル行を取得
                rows = await self.page.locator(f"{selector} tr").all()
                if not rows:
                    logger.debug(f"テーブル行が見つかりません: {selector}")
                    continue

                table_data = []
                for row in rows:
                    cells = await row.locator("td, th").all()
                    row_data = []
                    for cell in cells:
                        cell_text = await cell.text_content()
                        row_data.append(cell_text.strip() if cell_text else "")
                    if row_data:  # 空行をスキップ
                        table_data.append(row_data)

                if table_data:
                    logger.info(f"✅ テーブルデータ抽出成功: {selector_config.description} ({selector}) - {len(table_data)}行")

                    # 成功したセレクタの優先度を更新
                    if i > 0:
                        selectors_manager.update_selector_priority(workflow, category, element, selector)

                    return table_data

            except Exception as e:
                logger.debug(f"テーブルデータ抽出失敗 {selector}: {e}")
                continue

        # 第二層：MCPバックアップ
        if self.mcp_tools:
            logger.info(f"🔄 セレクタ失効、MCPテーブル抽出バックアップ試行: {selector_config.description}")
            try:
                mcp_data = await self.mcp_tools.smart_extract_table_fallback(selector_config.description)
                if mcp_data:
                    logger.info(f"✅ MCPテーブル抽出バックアップ成功: {selector_config.description} - {len(mcp_data)}行")
                    return mcp_data
            except Exception as e:
                logger.error(f"❌ MCPテーブル抽出も失敗: {e}")

        logger.warning(f"❌ すべてのセレクタとMCPが失敗: {selector_config.description}")
        return []