"""
Kaipoke登录服务
为其他工具提供统一的Kaipoke登录接口
"""
import os
from typing import Optional, Dict, Any
from logger_config import logger
from .kaipoke_common import KaipokeRPATools, KaipokeAccountManager


class KaipokeLoginService:
    """
    Kaipoke登录服务
    提供统一的登录接口，供其他工具使用
    """
    
    _instance = None
    _current_session = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(KaipokeLoginService, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.account_manager = KaipokeAccountManager()
            self.kaipoke_tools = None
            self.current_account_key = None
            self.initialized = True
    
    async def ensure_login(self, page, account_key: str = 'default', 
                          login_url: str = None) -> bool:
        """
        确保已登录到指定账号
        
        Args:
            page: Playwright页面对象
            account_key: 账号键值 ('default', 'nagayoshi', 'higashisengoku' 等)
            login_url: 登录URL（可选）
            
        Returns:
            bool: 登录是否成功
        """
        try:
            # 初始化RPA工具（如果需要）
            if self.kaipoke_tools is None or self.kaipoke_tools.page != page:
                self.kaipoke_tools = KaipokeRPATools(page)
            
            # 获取账号信息
            account = self.account_manager.get_account(account_key)
            if not account:
                logger.error(f"未找到账号配置: {account_key}")
                return False
            
            # 检查是否需要切换账号
            target_account = f"{account['corporation_id']}_{account['member_login_id']}"
            current_account = self.kaipoke_tools.get_current_account()
            
            if current_account == target_account:
                logger.info(f"已登录到目标账号: {account['name']}")
                return True
            
            # 执行登录
            if login_url is None:
                login_url = "https://r.kaipoke.biz/kaipokebiz/login/COM020102.do"
            
            logger.info(f"登录到账号: {account['name']} ({account_key})")
            success = await self.kaipoke_tools.login_with_account_switch(
                account['corporation_id'],
                account['member_login_id'],
                account['password'],
                login_url
            )
            
            if success:
                self.current_account_key = account_key
                logger.info(f"✅ 成功登录到账号: {account['name']}")
            else:
                logger.error(f"❌ 登录失败: {account['name']}")
            
            return success
            
        except Exception as e:
            logger.error(f"登录服务出错: {e}", exc_info=True)
            return False
    
    async def login_with_env_vars(self, page, corporation_id_env: str, 
                                 member_login_id_env: str, password_env: str,
                                 login_url: str = None) -> bool:
        """
        使用环境变量登录
        
        Args:
            page: Playwright页面对象
            corporation_id_env: 法人ID环境变量名
            member_login_id_env: 会员登录ID环境变量名
            password_env: 密码环境变量名
            login_url: 登录URL（可选）
            
        Returns:
            bool: 登录是否成功
        """
        try:
            # 从环境变量获取登录信息
            corporation_id = os.getenv(corporation_id_env)
            member_login_id = os.getenv(member_login_id_env)
            password = os.getenv(password_env)
            
            if not all([corporation_id, member_login_id, password]):
                logger.error(f"环境变量不完整: {corporation_id_env}, {member_login_id_env}, {password_env}")
                return False
            
            # 初始化RPA工具（如果需要）
            if self.kaipoke_tools is None or self.kaipoke_tools.page != page:
                self.kaipoke_tools = KaipokeRPATools(page)
            
            # 执行登录
            if login_url is None:
                login_url = "https://r.kaipoke.biz/kaipokebiz/login/COM020102.do"
            
            logger.info(f"使用环境变量登录: {corporation_id}/{member_login_id}")
            success = await self.kaipoke_tools.login_with_account_switch(
                corporation_id, member_login_id, password, login_url
            )
            
            if success:
                self.current_account_key = f"env_{corporation_id}_{member_login_id}"
                logger.info(f"✅ 环境变量登录成功")
            else:
                logger.error(f"❌ 环境变量登录失败")
            
            return success
            
        except Exception as e:
            logger.error(f"环境变量登录出错: {e}", exc_info=True)
            return False
    
    async def login_with_credentials(self, page, corporation_id: str, 
                                   member_login_id: str, password: str,
                                   login_url: str = None) -> bool:
        """
        使用直接凭据登录
        
        Args:
            page: Playwright页面对象
            corporation_id: 法人ID
            member_login_id: 会员登录ID
            password: 密码
            login_url: 登录URL（可选）
            
        Returns:
            bool: 登录是否成功
        """
        try:
            # 初始化RPA工具（如果需要）
            if self.kaipoke_tools is None or self.kaipoke_tools.page != page:
                self.kaipoke_tools = KaipokeRPATools(page)
            
            # 执行登录
            if login_url is None:
                login_url = "https://r.kaipoke.biz/kaipokebiz/login/COM020102.do"
            
            logger.info(f"使用直接凭据登录: {corporation_id}/{member_login_id}")
            success = await self.kaipoke_tools.login_with_account_switch(
                corporation_id, member_login_id, password, login_url
            )
            
            if success:
                self.current_account_key = f"direct_{corporation_id}_{member_login_id}"
                logger.info(f"✅ 直接凭据登录成功")
            else:
                logger.error(f"❌ 直接凭据登录失败")
            
            return success
            
        except Exception as e:
            logger.error(f"直接凭据登录出错: {e}", exc_info=True)
            return False
    
    def get_current_account_info(self) -> Optional[Dict[str, Any]]:
        """获取当前登录账号信息"""
        if self.current_account_key and self.current_account_key.startswith('env_'):
            return {
                'type': 'environment_variables',
                'account_key': self.current_account_key
            }
        elif self.current_account_key and self.current_account_key.startswith('direct_'):
            return {
                'type': 'direct_credentials',
                'account_key': self.current_account_key
            }
        elif self.current_account_key:
            account = self.account_manager.get_account(self.current_account_key)
            if account:
                return {
                    'type': 'managed_account',
                    'account_key': self.current_account_key,
                    'account_name': account['name'],
                    'corporation_id': account['corporation_id']
                }
        
        return None
    
    def get_available_accounts(self) -> Dict[str, Dict[str, Any]]:
        """获取所有可用账号"""
        return self.account_manager.get_all_accounts()
    
    async def logout(self):
        """登出当前账号"""
        if self.kaipoke_tools:
            await self.kaipoke_tools._complete_logout()
            self.current_account_key = None
            logger.info("已登出当前账号")


# 全局登录服务实例
kaipoke_login_service = KaipokeLoginService()


# 便捷函数，供其他工具直接使用
async def ensure_kaipoke_login(page, account_key: str = 'default', 
                              login_url: str = None) -> bool:
    """
    便捷函数：确保已登录到Kaipoke指定账号
    
    使用示例:
    ```python
    from core.rpa_tools.kaipoke_login_service import ensure_kaipoke_login
    
    # 登录到默认账号
    success = await ensure_kaipoke_login(page)
    
    # 登录到永吉据点
    success = await ensure_kaipoke_login(page, 'nagayoshi')
    ```
    """
    return await kaipoke_login_service.ensure_login(page, account_key, login_url)


async def kaipoke_login_with_env(page, corporation_id_env: str, 
                                member_login_id_env: str, password_env: str,
                                login_url: str = None) -> bool:
    """
    便捷函数：使用环境变量登录Kaipoke
    
    使用示例:
    ```python
    from core.rpa_tools.kaipoke_login_service import kaipoke_login_with_env
    
    success = await kaipoke_login_with_env(
        page,
        'KAIPOKE_CORPORATION_ID',
        'KAIPOKE_MEMBER_LOGIN_ID', 
        'KAIPOKE_PASSWORD'
    )
    ```
    """
    return await kaipoke_login_service.login_with_env_vars(
        page, corporation_id_env, member_login_id_env, password_env, login_url
    )


async def kaipoke_login_direct(page, corporation_id: str, member_login_id: str, 
                              password: str, login_url: str = None) -> bool:
    """
    便捷函数：使用直接凭据登录Kaipoke
    
    使用示例:
    ```python
    from core.rpa_tools.kaipoke_login_service import kaipoke_login_direct
    
    success = await kaipoke_login_direct(
        page, '235944', '4188', 'Rx2TEJHyc4'
    )
    ```
    """
    return await kaipoke_login_service.login_with_credentials(
        page, corporation_id, member_login_id, password, login_url
    )


def get_kaipoke_current_account() -> Optional[Dict[str, Any]]:
    """
    便捷函数：获取当前Kaipoke登录账号信息
    """
    return kaipoke_login_service.get_current_account_info()


def get_kaipoke_available_accounts() -> Dict[str, Dict[str, Any]]:
    """
    便捷函数：获取所有可用的Kaipoke账号
    """
    return kaipoke_login_service.get_available_accounts()


async def kaipoke_logout():
    """
    便捷函数：登出当前Kaipoke账号
    """
    await kaipoke_login_service.logout()