import asyncio
import os
from crewai import Task, Crew
from crewai.tools import BaseTool
from playwright.async_api import Page
from logger_config import logger
from typing import Type
from pydantic import BaseModel, ConfigDict
from core.browser.browser_manager import browser_manager
from .html_tools import ReadPageHTMLTool

class EmptyArgsSchema(BaseModel):
    model_config = ConfigDict(extra="ignore")  # 允许额外参数但忽略它们

# --- Pydanticモデルの定義 ---
# ... (既存のPydanticモデルは変更なし)

# --- 有状態ツールの定義 ---
class SmartBrowserTools(BaseTool):
    name: str = "Smart Browser Automation Tool"
    description: str = "ウェブページ上の要素をインテリジェントに操作するためのツールセット。"
    args_schema: Type[BaseModel] = EmptyArgsSchema

    def _run(self, **kwargs):
        """ブラウザを起動してページを取得します。"""
        try:
            # 非同期でブラウザを起動してページを取得
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 既にイベントループが実行中の場合は、新しいタスクとして実行
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self._ensure_browser_ready())
                    return future.result()
            else:
                return asyncio.run(self._ensure_browser_ready())
        except Exception as e:
            logger.error(f"ブラウザの初期化でエラーが発生しました: {e}")
            return f"ブラウザの初期化に失敗しました: {e}"

    async def _ensure_browser_ready(self):
        """ブラウザが準備できていることを確認します。"""
        try:
            if not browser_manager._browser:
                logger.info("ブラウザを起動します...")
                await browser_manager.start_browser(headless=False)

            page = await browser_manager.get_page()
            logger.info("ブラウザとページが正常に準備されました。")
            return "ブラウザとページが正常に準備されました。"
        except Exception as e:
            logger.error(f"ブラウザの準備でエラーが発生しました: {e}")
            return f"ブラウザの準備に失敗しました: {e}"

    async def intelligent_click(self, page: Page, target_description: str, stable_selector: str = None) -> str:
        # 延迟导入，避免循环依赖
        from agents.selector_finder_agent import selector_finder_agent
        logger.info(f"Intelligent Click実行：'{target_description}' をクリックしようとしています。")
        original_selector = stable_selector or f"text='{target_description}'"

        try:
            logger.info(f"試行1：セレクタ '{original_selector}' を使用します。")
            await page.click(original_selector, timeout=90000) # タイムアウトを延長
            logger.info(f"'{target_description}' のクリックに成功しました。")
            return f"'{target_description}' のクリックに成功しました。"
        except Exception as e:
            logger.warning(f"セレクタ '{original_selector}' でのクリックに失敗しました。理由: {e}")
            # 输出环境变量关键内容，便于排查 litellm 认证问题
            import os
            openai_key = os.environ.get('OPENAI_API_KEY')
            gemini_key = os.environ.get('GEMINI_API_KEY')
            if not openai_key and not gemini_key:
                logger.error("Neither OPENAI_API_KEY nor GEMINI_API_KEY is set. Skipping AI fallback.")
                return f"エラー：'{target_description}' のクリックに失敗しました。AIフォールバックはAPIキーがないためスキップされました。"
            
            logger.info("フォールバック：AIによるインテリジェントセレクタ検索を開始します...")

            # --- AIによるセレクタ検索とリトライ処理 ---
            try:
                page_html = await page.content()
                search_task_desc = f"""
                ウェブページのHTMLを分析し、以下の条件に最も一致する要素のCSSセレクタを特定してください。
                - **探している要素の説明**: '{target_description}'
                - **試行して失敗したセレクタ**: '{original_selector}'
                HTMLコンテンツ:
                ```html
                {page_html[:15000]}
                ```
                **注意**: 返り値はCSSセレクタの文字列のみとし、他の説明は一切含めないでください。
                """
                search_task = Task(
                    description=search_task_desc,
                    agent=selector_finder_agent,
                    expected_output="単一のCSSセレクタ文字列。例：'#main-content > a.button'"
                )

                finder_crew = Crew(agents=[selector_finder_agent], tasks=[search_task], verbose=False)
                new_selector = finder_crew.kickoff()

                if not new_selector or "not found" in new_selector.lower():
                    raise Exception(f"AIが新しいセレクタを見つけられませんでした。返答: {new_selector}")

                logger.info(f"AIが新しいセレクタを提案しました: '{new_selector}'")

                logger.info(f"試行2：新しいセレクタ '{new_selector}' を使用して再度クリックします。")
                await page.click(new_selector, timeout=15000)
                logger.info(f"リトライ成功！'{target_description}' のクリックに成功しました。")
                return f"'{target_description}' のクリックに成功しました（AIによるフォールバック）。"

            except Exception as retry_e:
                # litellm 认证异常特殊处理
                import traceback
                tb = traceback.format_exc()
                if 'AuthenticationError' in str(retry_e) or 'api_key' in str(retry_e):
                    logger.error(f"[litellm认证异常] 请检查 OPENAI_API_KEY 或 GEMINI_API_KEY 环境变量，或 .env 配置。详细: {retry_e}\n{tb}")
                    return f"エラー：API Key 认证失败，请检查 OPENAI_API_KEY 或 GEMINI_API_KEY 环境变量。详细: {retry_e}"
                logger.error(f"AIによるセレクタ検索とリトライ処理中にエラーが発生しました: {retry_e}\n{tb}", exc_info=True)
                return f"エラー：'{target_description}' のクリックに最終的に失敗しました。理由: {retry_e}"
    
    # ... (intelligent_download, select_dropdown_option, execute_js_for_date は変更なし) ...
    async def intelligent_download(self, page: Page, target_description: str, download_path: str, stable_selector: str = None) -> str:
        logger.info(f"Intelligent Download実行：'{target_description}' をクリックしてダウンロードを開始します。")
        try:
            async with page.expect_download() as download_info:
                click_result = await self.intelligent_click(page, target_description, stable_selector)
                if "エラー" in click_result:
                    raise Exception(f"ダウンロードボタンのクリックに失敗: {click_result}")
            
            download = await download_info.value
            save_path = os.path.join(download_path, download.suggested_filename)
            await download.save_as(save_path)
            
            logger.info(f"ファイルが正常にダウンロードされ、{save_path} に保存されました。")
            return f"ファイルが {save_path} に正常に保存されました。"
        except Exception as e:
            logger.error(f"Intelligent Download中にエラーが発生しました: {e}")
            return f"エラー：ファイルのダウンロードに失敗しました。理由: {e}"

    async def select_dropdown_option(self, page: Page, selector: str, value: str) -> str:
        logger.info(f"ドロップダウン操作：セレクタ '{selector}' で値 '{value}' を選択します。")
        try:
            if not selector or not value:
                return "情報不足のため、ドロップダウン操作をスキップしました。"
            
            await page.select_option(selector, value=value, timeout=15000)
            logger.info(f"ドロップダウンで '{value}' を選択しました。")
            return f"セレクタ '{selector}' でオプション '{value}' の選択に成功しました。"
        except Exception as e:
            logger.error(f"ドロップダウン操作中にエラーが発生しました: {e}")
            return f"エラー：ドロップダウンオプションの選択に失敗しました。理由: {e}"

    async def execute_js_for_date(self, page: Page, date_handling_method: str) -> str:
        logger.info(f"日付選択JSを実行します。方法: {date_handling_method}")
        if date_handling_method == "SELECT_PREVIOUS_MONTH_JS":
            # 优先尝试 select_option
            try:
                from datetime import datetime, timedelta
                today = datetime.now()
                last_month = today.replace(day=1) - timedelta(days=1)
                target_month = f"{last_month.month:02d}"
                await page.select_option('#targetMonth', value=target_month)
                logger.info(f"select_optionで {target_month} 月を選択しました。")
                return f"select_optionで {target_month} 月を選択しました。"
            except Exception as e:
                logger.warning(f"select_optionでの月選択に失敗: {e}，JSでリトライ")
                js_code = """
                () => {
                    var today = new Date();
                    var lastMonth = new Date(today);
                    lastMonth.setMonth(today.getMonth() - 1);
                    var targetMonth = (lastMonth.getMonth() + 1).toString().padStart(2, "0");
                    var selectElement = document.querySelector("#targetMonth");
                    if (selectElement) {
                        for (var i = 0; i < selectElement.options.length; i++) {
                            if (selectElement.options[i].value === targetMonth) {
                                selectElement.selectedIndex = i;
                                var event = new Event("change", { bubbles: true });
                                selectElement.dispatchEvent(event);
                                return `Success: Selected month ${targetMonth}`;
                            }
                        }
                        return `Failure: Month ${targetMonth} not found`;
                    } else {
                        return `Failure: Selector #targetMonth not found`;
                    }
                }
                """
                result = await page.evaluate(js_code)
                logger.info(f"日付選択JSの実行結果: {result}")
                return f"日付選択JSの実行に成功しました: {result}"
        elif date_handling_method == "TYPE_PREVIOUS_MONTH":
            js_code = """
            () => {
                var today = new Date();
                var lastMonthDate = new Date(today);
                lastMonthDate.setMonth(today.getMonth() - 1);
                var year = lastMonthDate.getFullYear();
                var month = lastMonthDate.getMonth() + 1;
                window.targetYear = year;
                window.targetMonth = month;
                return `Success: Set window.targetYear=${year}, window.targetMonth=${month}`;
            }
            """
            result = await page.evaluate(js_code)
            logger.info(f"日付選択JSの実行結果: {result}")
            year = await page.evaluate("window.targetYear")
            month = await page.evaluate("window.targetMonth")
            await page.fill('#serviceyearkey', str(year))
            await page.select_option('#servicemonthkey', value=f"{month:02d}")
            logger.info(f"年・月フィールドに {year} / {month} を入力しました。")
            return f"日付選択JSの実行に成功しました: {result}"
        else:
            return "日付処理方法が指定されていないか、無効なため、スキップしました。"

async def do_download_task(page, params, download_path, smart_tools, selector_agent, task_id: str = None):
    """
    按 params 自动化下载流程，选择器失效时用 agent 智能兜底。
    :param page: Playwright Page
    :param params: 任务参数 dict
    :param download_path: 下载保存路径
    :param smart_tools: SmartBrowserTools 实例
    :param selector_agent: selector_finder_agent
    :param task_id: 任务ID，用于特殊逻辑判断
    """
    # 0. 登录后，点击“業務帳票”
    try:
        await smart_tools.intelligent_click(page, params.get("main_menu_target", "業務帳票"))
    except Exception as e:
        logger.warning(f"業務帳票クリック失败: {e}")
    # 1. 点击二级菜单
    try:
        await smart_tools.intelligent_click(page, params.get("report_menu_target", "010 請求状況一覧"))
    except Exception as e:
        logger.warning(f"二级菜单クリック失败: {e}")
    # 2. 选择請求年月（上个月）
    try:
        await smart_tools.execute_js_for_date(page, params.get("date_handling_method"))
    except Exception as e:
        logger.warning(f"年月选择JS失败: {e}")
    
    # 3. 根据task_id决定是否点击“検索”按钮
    if task_id != "kanamic_1690_chiki_mitchaku":
        try:
            await smart_tools.intelligent_click(page, "検索")
        except Exception as e:
            logger.warning(f"検索按钮点击失败: {e}")
    else:
        logger.info(f"タスクID '{task_id}' のため、'検索' ボタンのクリックをスキップします。")

    # 4. 勾选目标复选框（优先按 selector，兼容旧逻辑）
    checkbox_selectors = params.get("checkbox_selectors")
    if checkbox_selectors:
        not_found = []
        for selector in checkbox_selectors:
            try:
                await page.check(selector)
                logger.info(f"复选框（{selector}）勾选成功")
            except Exception as e:
                logger.warning(f"复选框（{selector}）勾选失败: {e}")
                not_found.append(selector)
        if not_found:
            logger.warning(f"以下selector未能勾选：{not_found}")
    else:
        checkbox_targets = params.get("checkbox_targets")
        if checkbox_targets:
            not_found = []
            rows = await page.query_selector_all('#dataItems tr')
            # 先缓存每行第3列文本，避免重复请求
            row_td3_texts = []
            for idx, row in enumerate(rows):
                td3 = await row.query_selector('td:nth-child(3)')
                td3_text = (await td3.inner_text()).replace(' ', '').replace('\u3000', '').replace('\n', '').replace('\r', '').lower().strip() if td3 else ''
                row_td3_texts.append(td3_text)
                logger.debug(f"[复选框调试] 行{idx}第3列: '{td3_text}'")
            for target_name in checkbox_targets:
                target_norm = target_name.replace(' ', '').replace('\u3000', '').replace('\n', '').replace('\r', '').lower().strip()
                matched = False
                for idx, td3_text in enumerate(row_td3_texts):
                    logger.debug(f"[复选框调试] 行{idx}第3列: '{td3_text}', 目标: '{target_norm}'")
                    if target_norm in td3_text:
                        row = rows[idx]
                        checkbox = await row.query_selector('input[type=checkbox]')
                        if checkbox:
                            try:
                                await checkbox.check()
                                logger.info(f"复选框（{target_name}）勾选成功，行{idx}")
                                matched = True
                            except Exception as ce:
                                logger.warning(f"复选框（{target_name}）勾选异常: {ce}")
                        else:
                            logger.warning(f"未找到行{idx}内复选框: {target_name}")
                if not matched:
                    logger.warning(f"未找到包含事业所名 '{target_name}' 的行，无法勾选复选框")
                    not_found.append(target_name)
            if not_found:
                logger.warning(f"以下目标未能勾选：{not_found}")
        elif params.get("checkbox_target"):
            try:
                await page.check("input[type=checkbox]")
            except Exception as e:
                logger.warning(f"复选框勾选失败: {e}")
                logger.warning(f"复选框勾选失败: {e}")
    # 5. 点击下载按钮
    try:
        # 防御性移除 crewai 注入的 security_context 字段
        if isinstance(params, dict):
            params.pop("security_context", None)
        result = await smart_tools.intelligent_download(
            page,
            params.get("download_button_target"),
            download_path
        )
        logger.info(f"下载结果: {result}")

        # 下载后自动跳转回首页
        login_url = params.get("login_url")
        if login_url:
            home_url = login_url.rsplit("/", 1)[0] + "/"
            try:
                await page.goto(home_url)
                logger.info(f"下载后已跳转回首页: {home_url}")
            except Exception as e:
                logger.warning(f"下载后跳转首页失败: {e}")
        else:
            logger.warning("params 未包含 login_url，无法跳转首页。")

        return result
    except Exception as e:
        logger.error(f"下载失败: {e}")
        return f"下载失败: {e}"