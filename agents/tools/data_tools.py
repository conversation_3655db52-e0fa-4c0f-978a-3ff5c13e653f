import os
import csv
import glob
from datetime import datetime
from logger_config import logger
import pandas as pd

def process_downloaded_csv(download_path: str, file_name_template: str, original_encoding: str = 'cp932', target_encoding: str = 'UTF-8', data_start_row: int = 2) -> str:
    """指定されたダウンロードパスから最新のCSVファイルを見つけ、文字コードを変換し、
    テンプレートに基づいて新しいファイル名を生成し、そのXLSXファイルのパスを返すツール。

    Args:
        download_path (str): ダウンロードされたファイルが保存されているディレクトリ。
        file_name_template (str): 新しいファイル名のテンプレート。

    Returns:
        str: 処理後のXLSXファイルのパス。エラーの場合はエラーメッセージを返す。
    """
    try:
        logger.info(f"CSV処理を開始します。ダウンロードパス: {download_path}")

        list_of_files = glob.glob(f'{download_path}/*.csv')
        if not list_of_files:
            raise FileNotFoundError(f"ダウンロードパスにCSVファイルが見つかりません: {download_path}")
        latest_file = max(list_of_files, key=os.path.getctime)
        logger.info(f"最新のファイルを発見: {latest_file}")

        # CSVを手動で解析（複雑な構造に対応）
        try:
            # まず原始データを読み取り
            with open(latest_file, 'r', encoding=original_encoding) as f:
                lines = f.readlines()

            logger.info(f"CSVファイルから{len(lines)}行を読み取りました")

            # 実際のデータ行を見つける（設定可能な開始行）
            data_lines = []
            skip_rows = data_start_row - 1  # 0ベースインデックスに変換
            
            logger.info(f"データ開始行: {data_start_row}行目（{skip_rows}行をスキップ）")
            
            for i, line in enumerate(lines):
                if i >= skip_rows:
                    # 空行をスキップ
                    if line.strip():
                        data_lines.append(line.strip())

            logger.info(f"データ行数: {len(data_lines)}")

            if not data_lines:
                raise ValueError("データ行が見つかりません")

            # CSVデータを解析
            import csv
            from io import StringIO

            # 全ての行をCSVとして解析（表頭を含む）
            all_lines = [lines[0].strip()] + data_lines  # 表頭 + データ行
            csv_data = []
            csv_reader = csv.reader(all_lines)
            
            # 最初の行を列名として使用
            headers = next(csv_reader)
            logger.info(f"CSV表頭: {headers}")
            
            # データ行を読み込み
            for row in csv_reader:
                csv_data.append(row)

            # DataFrameに変換（表頭付き）
            df = pd.DataFrame(csv_data, columns=headers)
            logger.info(f"CSVファイルを正常に読み込みました。形状: {df.shape}")
            logger.info(f"列名: {list(df.columns)}")

        except Exception as e:
            logger.error(f"手動CSV解析に失敗しました: {e}")
            # フォールバック：標準的な方法を試す
            try:
                df = pd.read_csv(latest_file, encoding=original_encoding, header=0,
                               sep=None, engine='python', on_bad_lines='skip')
                logger.warning("フォールバック解析を使用しました（表頭付き）")
                logger.info(f"フォールバック列名: {list(df.columns)}")
            except Exception as e2:
                logger.error(f"フォールバック解析も失敗しました: {e2}")
                raise

        template_vars = {
            'datetime': datetime.now().strftime('%Y-%m-%d_%H%M'),
            'year': datetime.now().year,
            'month': datetime.now().month
        }
        # テンプレート変数を解決
        import re
        placeholders = re.findall(r'\{(.+?)\}', file_name_template)
        for placeholder in placeholders:
            if placeholder.startswith('cell_'):
                try:
                    # 例: cell_B2 -> row=1, col=1
                    col_str = placeholder[5:6]
                    row_str = placeholder[6:]
                    row_idx = int(row_str) - 1
                    col_idx = ord(col_str.upper()) - ord('A')
                    if row_idx < len(df) and col_idx < len(df.columns):
                        template_vars[placeholder] = df.iat[row_idx, col_idx]
                    else:
                        template_vars[placeholder] = '' # セルが存在しない場合は空文字
                except (ValueError, IndexError) as e:
                    logger.warning(f"テンプレート変数 '{placeholder}' の解決に失敗: {e}")
                    template_vars[placeholder] = ''
            elif placeholder not in template_vars:
                 template_vars[placeholder] = '' # その他の未定義変数は空文字

        new_filename_xlsx = file_name_template.format(**template_vars)
        new_filepath_xlsx = os.path.join(download_path, new_filename_xlsx)

        df.to_excel(new_filepath_xlsx, index=False, header=True)
        logger.info(f"XLSXとして保存しました: {new_filepath_xlsx}")

        os.remove(latest_file)
        logger.info(f"元のCSVファイル {latest_file} を削除しました。")

        return new_filepath_xlsx

    except Exception as e:
        logger.error(f"CSV処理中にエラーが発生しました: {e}", exc_info=True)
        return f"エラー: CSVファイルの処理に失敗しました。理由: {e}"

def process_umegaoka_debt_csv(download_path: str, file_name_template: str, original_encoding: str = 'cp932') -> str:
    """梅ヶ丘債権データ専用のCSV処理関数
    
    梅ヶ丘債権データの特徴：
    - 第1行：表頭
    - 第2行以降：データ
    
    Args:
        download_path (str): ダウンロードされたファイルが保存されているディレクトリ
        file_name_template (str): 新しいファイル名のテンプレート
        original_encoding (str): 元のファイルエンコーディング
    
    Returns:
        str: 処理後のXLSXファイルのパス
    """
    try:
        logger.info(f"梅ヶ丘債権データCSV処理を開始します。ダウンロードパス: {download_path}")
        
        # 最新のCSVファイルを取得
        list_of_files = glob.glob(f'{download_path}/*.csv')
        if not list_of_files:
            raise FileNotFoundError(f"ダウンロードパスにCSVファイルが見つかりません: {download_path}")
        latest_file = max(list_of_files, key=os.path.getctime)
        logger.info(f"最新のファイルを発見: {latest_file}")
        
        # 梅ヶ丘債権データ専用の処理
        try:
            # 標準的なCSV読み込み（第1行がヘッダー、第2行以降がデータ）
            df = pd.read_csv(latest_file, encoding=original_encoding, header=0)
            logger.info(f"梅ヶ丘債権データを正常に読み込みました。形状: {df.shape}")
            logger.info(f"列名: {list(df.columns)}")
            
        except Exception as e:
            logger.error(f"標準CSV読み込み失敗: {e}")
            # フォールバック：手動解析
            with open(latest_file, 'r', encoding=original_encoding) as f:
                lines = f.readlines()
            
            logger.info(f"手動解析：{len(lines)}行を読み取り")
            
            if len(lines) < 2:
                raise ValueError("データが不足しています（最低2行必要）")
            
            # 第1行をヘッダーとして使用
            header_line = lines[0].strip()
            data_lines = [line.strip() for line in lines[1:] if line.strip()]
            
            logger.info(f"ヘッダー: {header_line}")
            logger.info(f"データ行数: {len(data_lines)}")
            
            # CSVデータを解析
            import csv
            from io import StringIO
            
            # ヘッダーを解析
            header_reader = csv.reader([header_line])
            headers = next(header_reader)
            
            # データ行を解析
            csv_data = []
            csv_reader = csv.reader(data_lines)
            for row in csv_reader:
                csv_data.append(row)
            
            # DataFrameに変換
            df = pd.DataFrame(csv_data, columns=headers)
            logger.info(f"手動解析完了。形状: {df.shape}")
        
        # テンプレート変数を解決
        template_vars = {
            'datetime': datetime.now().strftime('%Y-%m-%d_%H%M'),
            'year': datetime.now().year,
            'month': datetime.now().month
        }
        
        # セル参照を解決
        import re
        placeholders = re.findall(r'\{(.+?)\}', file_name_template)
        for placeholder in placeholders:
            if placeholder.startswith('cell_'):
                try:
                    # 例: cell_C2 -> row=1, col=2
                    col_str = placeholder[5:6]
                    row_str = placeholder[6:]
                    row_idx = int(row_str) - 1
                    col_idx = ord(col_str.upper()) - ord('A')
                    if row_idx < len(df) and col_idx < len(df.columns):
                        template_vars[placeholder] = df.iat[row_idx, col_idx]
                    else:
                        template_vars[placeholder] = ''
                except (ValueError, IndexError) as e:
                    logger.warning(f"テンプレート変数 '{placeholder}' の解決に失敗: {e}")
                    template_vars[placeholder] = ''
            elif placeholder not in template_vars:
                template_vars[placeholder] = ''
        
        # 新しいファイル名を生成
        new_filename_xlsx = file_name_template.format(**template_vars)
        new_filepath_xlsx = os.path.join(download_path, new_filename_xlsx)
        
        # XLSXとして保存（ヘッダー付き）
        df.to_excel(new_filepath_xlsx, index=False, header=True)
        logger.info(f"梅ヶ丘債権データをXLSXとして保存しました: {new_filepath_xlsx}")
        
        # 元のCSVファイルを削除
        os.remove(latest_file)
        logger.info(f"元のCSVファイル {latest_file} を削除しました。")
        
        return new_filepath_xlsx
        
    except Exception as e:
        logger.error(f"梅ヶ丘債権データCSV処理中にエラーが発生しました: {e}", exc_info=True)
        return f"エラー: 梅ヶ丘債権データCSVファイルの処理に失敗しました。理由: {e}"