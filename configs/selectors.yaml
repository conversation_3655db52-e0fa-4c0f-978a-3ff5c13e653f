kanamic:
  login:
    username_field:
    - '#josso_username'
    - input[name='josso_username']
    - input[type='text'][placeholder*='ユーザー']
    password_field:
    - '#josso_password'
    - input[name='josso_password']
    - input[type='password']
    login_button:
    - '#form > form > div.submit-container.lastChild > input'
    - .submit-button
    - input[type='submit']
    - button[type='submit']
    - .login-btn
  download:
    login_button:
    - '#form > form > div.submit-container.lastChild > input'
    - .submit-button
    - input[type='submit']
    - button[type='submit']
    - .login-btn
    main_menu:
    - a:nth-of-type(25) .btn
    - text='債権・会計'
    - text='業務帳票'
    - '[href*=''debt'']'
    - '[href*=''business'']'
    - '[href*=''accounting'']'
    report_menu:
    - '#isA17t123-1 li:nth-of-type(2) .saikenmenu1'
    - text='債権管理'
    - text='010 請求状況一覧'
    - '[href*=''debt-management'']'
    - '[href*=''billing'']'
    - '[href*=''receivable'']'
    service_type_dropdown:
    - '#keyFlag'
    - select[name='keyFlag']
    - select[id='keyFlag']
    - select[name='service_type']
    - .service-type-select
    service_type_option:
    - option[value='CONTRACT_FLAG999']
    - option:contains('居宅介護支援')
    - option[value*='999']
    - option[text*='居宅介護支援']
    checkbox_item0:
    - '#checkItem0'
    - input[id='checkItem0']
    - input[name='checkItem0']
    - input[type='checkbox'][value*='福岡居宅']
    checkbox_item2:
    - '#checkItem2'
    - input[id='checkItem2']
    - input[name='checkItem2']
    - input[type='checkbox'][value*='梅ヶ丘']
    checkbox_item4:
    - '#checkItem4'
    - input[id='checkItem4']
    - input[name='checkItem4']
    - input[type='checkbox'][value*='居宅介護支援']
    date_year_select:
    - '#serviceyearkey'
    - select[name='serviceyearkey']
    - select[id='serviceyearkey']
    - select[name='year']
    - '#year'
    date_month_select:
    - '#servicemonthkey'
    - select[name='servicemonthkey']
    - select[id='servicemonthkey']
    - select[name='month']
    - '#month'
    search_button:
    - '#doSearch'
    - input[id='doSearch']
    - button[id='doSearch']
    - input[type='submit'][value*='検索']
    - button:contains('検索')
    - .search-btn
    download_button:
    - '#doCsv'
    - '#doCsv-2'
    - input[id='doCsv']
    - input[id='doCsv-2']
    - button[id='doCsv']
    - button[id='doCsv-2']
    - text='CSV出力'
    - button:contains('CSV出力')
    - input[value*='CSV出力']
  csv_download:
    menu_link_28:
    - a:nth-of-type(28) .btn
    - a:nth-of-type(28)
    - .menu-item:nth-child(28) a
    - text=分析サービス
    button_b:
    - .button-B
    - button.btn-b
    - '[data-action=''button-b'']'
    - text=LIFE用データ出力
    service_type_select:
    - '#servicetype'
    - select[name='servicetype']
    service_type_option_54:
    - option[value='54']
    target_month_select:
    - '#targetMonth'
    - select[name='targetMonth']
    search_button:
    - '#searchbtn'
    - button:contains('検索')
    - .search-btn
    checkbox_mb5:
    - .mb5 span
    - .mb5 input[type='checkbox']
    checkbox_rowb:
    - .rowB p:nth-of-type(2) span
    - .rowB input[type='checkbox']
    download_button:
    - '#predlbtn'
    - '#dlbtn'
    - button:contains('確認用CSV')
    - button:contains('ダウンロード')
    - .download-btn
kaipoke:
  login:
    corporation_id:
    - '#form\:corporation_id'
    - input[name='corporation_id']
    - '#corporation_id'
    - input[name='corporationId']
    member_login_id:
    - '#form\:member_login_id'
    - input[name='member_login_id']
    - '#member_login_id'
    - input[name='memberLoginId']
    password:
    - '#form\:password'
    - input[name='password']
    - input[type='password']
    login_button:
    - '#form\:logn_nochklogin'
    error_page_continue_button:
    - .box-btn a
kaipoke_billing:
  navigation:
    receipt_menu:
    - .mainCtg li:nth-of-type(1) a
    - text='レセプト'
    info_output_menu:
    - '#jsddm > :nth-child(8) img'
    - .dropdown:nth-child(8) .dropdown-toggle
    - text='各種情報出力'
    output_target_selection:
    - text='出力対象選択'
    - .dropdown:nth-child(8) li a
  billing:
    user_billing:
    - text='利用者請求'
    - '#billingInfo_tooltip'
    csv_export:
    - '#form\:export img'
    - text='CSV出力'
  performance_report:
    main_menu_receipt:
    - .mainCtg li:nth-of-type(1) a
    facility_fukuoka:
    - ul:nth-of-type(2) > :nth-child(3) :nth-child(3) a
    facility_kagoshima:
    - ul:nth-of-type(2) > :nth-child(1) :nth-child(2) a
    submenu_schedule_performance:
    - '#jsddm > :nth-child(3) img'
    submenu_schedule_performance_link:
    - '#jsddm > :nth-child(3) li a'
    year_month_select:
    - '#form\:serviceOfferYmSelectId'
    search_conditions_button:
    - '#btn-search img'
    checkbox_plan_data_0:
    - '#form\:create_plan_data\:0'
    checkbox_achievement_data_0:
    - '#form\:create_achieviement_data\:0'
    checkbox_plan_data_1:
    - '#form\:create_plan_data\:1'
    checkbox_achievement_data_1:
    - '#form\:create_achieviement_data\:1'
    checkbox_plan_data_2:
    - '#form\:create_plan_data\:2'
    confirm_search_conditions_button:
    - .box-search-btn div:nth-of-type(1) img
    page_info_text:
    - .pager-btm :nth-child(3)
    next_page_button:
    - .pager-btm .next02 a
    - .pager-btm .next a
    - .next02 a:not([href*='month']):not([href*='date'])
    - .next a:not([href*='month']):not([href*='date'])
    - a:contains('次へ'):not([href*='month'])
    user_list_links:
    - '#form > div.box-btn.mar-b-0.clearfix > table > tbody > tr > td:nth-child(1)
      > a'
    - '#form table tbody tr td:nth-child(1) a'
    - .box-btn table tbody tr td:nth-child(1) a
    - table tbody tr td:nth-child(1) a
    user_name_extract:
    - .color-neutral:nth-child(2) td:nth-of-type(1)
    table_data_tbody:
    - '#tableData > tbody'
    - '#tableData tbody'
    - table#tableData tbody
    - .data-table tbody
    - table tbody
    table_date:
    - '#tableData tr:nth-of-type(1) td:nth-of-type(1)'
    table_weekday:
    - '#tableData tr:nth-of-type(1) td:nth-of-type(2)'
    table_service_content_plan:
    - '#tableData tr:nth-of-type(1) :nth-child(4)'
    table_insurance_plan:
    - '#tableData tr:nth-of-type(1) :nth-child(5)'
    table_service_time_plan:
    - '#tableData tr:nth-of-type(1) :nth-child(6)'
    table_plan_time:
    - '#tableData tr:nth-of-type(1) :nth-child(7)'
    table_service_content_actual:
    - '#tableData tr:nth-of-type(1) :nth-child(11)'
    table_insurance_actual:
    - '#tableData tr:nth-of-type(1) :nth-child(12)'
    table_service_time_actual:
    - '#tableData tr:nth-of-type(1) :nth-child(13)'
    table_calculation_time:
    - '#tableData tr:nth-of-type(1) :nth-child(14)'
    table_dispatch_count:
    - '#tableData tr:nth-of-type(1) .colum09'
    table_remarks:
    - '#tableData tr:nth-of-type(1) .colum06'
    data_table_row_1_col_5:
    - '#tableData tr:nth-of-type(1) :nth-child(5)'
    - .data-table tr:first-child td:nth-child(5)
    data_table_row_1_col_6:
    - '#tableData tr:nth-of-type(1) :nth-child(6)'
    - .data-table tr:first-child td:nth-child(6)
    data_table_row_1_col_7:
    - '#tableData tr:nth-of-type(1) :nth-child(7)'
    - .data-table tr:first-child td:nth-child(7)
    data_table_row_1_col_11:
    - '#tableData tr:nth-of-type(1) :nth-child(11)'
    - .data-table tr:first-child td:nth-child(11)
    data_table_row_1_col_12:
    - '#tableData tr:nth-of-type(1) :nth-child(12)'
    - .data-table tr:first-child td:nth-child(12)
    data_table_row_1_col_13:
    - '#tableData tr:nth-of-type(1) :nth-child(13)'
    - .data-table tr:first-child td:nth-child(13)
    data_table_row_1_col_14:
    - '#tableData tr:nth-of-type(1) :nth-child(14)'
    - .data-table tr:first-child td:nth-child(14)
    data_table_row_1_col_dispatch_count:
    - '#tableData tr:nth-of-type(1) .colum09'
    - .data-table tr:first-child .dispatch-count
    data_table_row_1_col_remarks:
    - '#tableData tr:nth-of-type(1) .colum06'
    - .data-table tr:first-child .remarks
    back_to_list_button:
    - text='一覧に戻る'
    - a:contains('一覧に戻る')
    - a[href*='list']
    - .btn-back
    next_user_button:
    - .table-linkuser td:nth-of-type(3) a
    - .table-linkuser td:nth-child(3) a
    - .user-navigation td:nth-of-type(3) a
    user_id_validation:
    - '#form\:userId'
    - '#form\:user_id'
    - input[name*='userId']
    user_detail_page_indicators:
    - '#tableData'
    - .table-linkuser
    - '#form\:userId'
    first_user_link:
    - .color-neutral:nth-child(2) a
    - .color-neutral:nth-of-type(2) a
    - tr:nth-child(2) td:nth-child(1) a
  monthly_report:
    receipt_menu:
    - .mainCtg li:nth-of-type(1) a
    service_center_xpath:
    - //a[contains(text(), "{element_text}")]
    info_output_hover:
    - li:nth-of-type(7) img
    output_target_selection:
    - li:nth-of-type(7) li a
    monthly_result_tip:
    - '#form\:useMonthlyScheduleTip span'
    year_month_select:
    - '#form\:serviceOfferYm'
    result_division:
    - '#form\:planAchieveDivision\:1'
    excel_export_button:
    - '#form\:export img'
    performance_menu:
    - text='実績管理'
    - a:contains('実績管理')
    - '[data-menu=''実績管理'']'
    monthly_performance_list:
    - text='月次実績一覧'
    - a:contains('月次実績一覧')
    - '[data-submenu=''月次実績一覧'']'
    location_dropdown:
    - '#location_select'
    - select[name='location']
    - .location-select
    previous_month_button:
    - text='前月'
    - button:contains('前月')
    - .prev-month
    search_button:
    - text='検索'
    - button:contains('検索')
    - '#search_btn'
    - .search-button
  daily_performance_report:
    receipt_menu:
    - .mainCtg li:nth-of-type(1) a
    facility_kagoshima_homecare:
    - ul:nth-of-type(2) > :nth-child(1) li:nth-of-type(1) a
    schedule_performance_hover:
    - li:nth-of-type(4):nth-child(7) img
    - '#jsddm > :nth-child(4) img'
    - .menu-item:nth-child(4) img
    daily_performance_registration:
    - li:nth-of-type(4):nth-child(7) li:nth-of-type(2) a
    - '#jsddm > :nth-child(4) li:nth-child(2) a'
    - .submenu li:nth-child(2) a
    service_offer_month_select:
    - '#form\:serviceOfferYmSelectId'
    - select[id='form:serviceOfferYmSelectId']
    - '#serviceOfferYmSelectId'
    service_offer_day_select:
    - '#form\:serviceOfferDaySelectId'
    - select[id='form:serviceOfferDaySelectId']
    - '#serviceOfferDaySelectId'
    page_title_confirm:
    - '#page_title'
    - .page-title
    - h1
    achievement_filter_checkbox:
    - .box-refine-l :nth-child(2)
    - .filter-checkbox:nth-child(2)
    - input[type='checkbox']:nth-child(2)
    data_table_tbody:
    - '#tblMem094501 > tbody'
    - '#tblMem094501 tbody'
    - table#tblMem094501 tbody
    - .data-table tbody
    total_records_display:
    - .pager-btm p
    - .pagination-info p
    - .pager-bottom p
    page_info_display:
    - .pager-btm :nth-child(3)
    - .pagination-info :nth-child(3)
    - .page-info
    next_page_button:
    - div.pager-btm li.next02 a
    - .next02 a
    - .pagination .next a
    current_page_indicator:
    - .pager-btm :nth-child(3)
    - .pagination-current
    - .page-current
common:
  buttons:
    submit:
    - input[type='submit']
    - button[type='submit']
    - .submit-btn
    - .btn-submit
    search:
    - button:contains('検索')
    - input[value='検索']
    - '#search'
    - .search-btn
    download:
    - button:contains('ダウンロード')
    - a:contains('ダウンロード')
    - .download-btn
    - '#download'
    excel:
    - button:contains('Excel')
    - a:contains('Excel')
    - .excel-btn
    - '#excel'
  form_elements:
    text_input:
    - input[type='text']
    - input:not([type])
    password_input:
    - input[type='password']
    select_dropdown:
    - select
    checkbox:
    - input[type='checkbox']
    radio:
    - input[type='radio']
kaipoke_tennki:
  navigation:
    main_menu:
    - .mainCtg li:nth-of-type(1) a
    - text='レセプト'
    facility_selection:
    - xpath=//a[contains(text(), "訪問看護/4660190861")]
    - xpath=//a[contains(text(), "訪問看護/4664590280")]
    - xpath=//a[contains(text(), "訪問看護/4660191471")]
    - xpath=//a[contains(text(), "訪問看護/4060391200")]
    nursing_menu_hover:
    - .dropdown:nth-child(3) .dropdown-toggle
    nursing_menu_click:
    - .dropdown:nth-child(3) li:nth-of-type(2) a
    year_month_select:
    - '#selectServiceOfferYm'
  user_selection:
    user_dropdown:
    - .pulldownUser .form-control
    - select[class*="pulldownUser"]
    - select[name*="user"]
  form:
    add_button:
    - '#btn_area .cf:nth-child(1) :nth-child(1)'  # 🆕 用户推荐的精确选择器
    - 'button:has-text("新規追加")'
    - text='新規追加'
    - button:contains('新規追加')
    - 'input[value="新規追加"]'
    submit_button:
    - '#btnRegisPop'
    - text='登録'
    - button:contains('登録')
    insurance_kaigo:
    - '#inPopupInsuranceDivision01'
    insurance_iryou:
    - '#inPopupInsuranceDivision02'
    insurance_jihi:
    - '#inPopupInsuranceDivision03'
    form_modal:
    - '#registModal'
    - .modal:has(#inPopupInsuranceDivision01)
    - .modal:has(#inPopupInsuranceDivision02)
    form_active_indicators:
    - '#inPopupInsuranceDivision01:visible'
    - '#inPopupInsuranceDivision02:visible'
    - '#inPopupServiceKindId:visible'
    - '#btnRegisPop:visible'
    service_kind_select:
    - '#inPopupServiceKindId'
    jihi_category:
    - '#inPopupInsuranceOtherCategoryName'
    jihi_amount:
    - '#inPopupAmount'
    jihi_estimation_time:
    - '#inPopupEstimationTime'
    estimate_fields:
    - '#inPopupEstimate1'
    - '#inPopupEstimate2'
    - '#inPopupEstimate3'
    - '#inPopupEstimate4'
    - '#inPopupEstimate5'
    estimate_field_status:
    - '#inPopupEstimate2[disabled]'
    - '#inPopupEstimate3[disabled]'
    - '#inPopupEstimate4[disabled]'
    - '#inPopupEstimate5[disabled]'
    estimate_field_enabled:
    - '#inPopupEstimate2:not([disabled])'
    - '#inPopupEstimate3:not([disabled])'
    - '#inPopupEstimate4:not([disabled])'
    - '#inPopupEstimate5:not([disabled])'
    time_fields:
    - '#inPopupStartHour'
    - '#inPopupStartMinute1'
    - '#inPopupStartMinute2'
    - '#inPopupEndHour'
    - '#inPopupEndMinute1'
    - '#inPopupEndMinute2'
    staff_button:
    - '#input_staff_on .btn'
    staff_job_division:
    - '#chargeStaff1JobDivision1'
    plan_achievements:
    - '#inPopupPlanAchievementsDivision02'
    service_content:
    - '#inPopupserviceContentId1'
    protected_elements:
    - '#registModal'
    - '#inPopupInsuranceDivision01'
    - '#inPopupInsuranceDivision02'
    - '#inPopupServiceKindId'
    - '#btnRegisPop'
    - '.modal:has(#inPopupInsuranceDivision01, #inPopupInsuranceDivision02)'
    blocking_modal_detection:
    - '.modal:not(#registModal)'
    - '.modal-backdrop:not([data-form-related])'
    - '.overlay:not([data-form-related])'
    - '[style*="z-index"][style*="999"]:not([id*="inPopup"])'
    form_detection:
    - '#registModal:visible'
    - '#inPopupInsuranceDivision01:visible'
    - '#inPopupInsuranceDivision02:visible'
    - '.modal:has(#inPopupServiceKindId):visible'
    button_blocking_elements:
    - '[style*="z-index: 999"]:not(#registModal)'
    - '.modal-backdrop:not([data-form-related])'
    - '[class*="karte"]:not([data-form-related])'
timeouts:
  default: 30000
  login: 60000
  download: 120000
  navigation: 45000
wait_strategies:
  default: networkidle
  after_click: domcontentloaded
  after_navigation: networkidle
