#!/usr/bin/env python3
"""
简化的Kaiopetennki工作流程测试脚本
验证移除窗口处理逻辑后的工作流程稳定性
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from logger_config import logger
from core.browser.browser_manager import browser_manager
from core.selector_executor import SelectorExecutor
from core.rpa_tools.kaipoke_login_service import kaipoke_login_with_env
from core.rpa_tools.tennki_form_engine import TennkiFormEngine


class SimplifiedKaiopetennkiTester:
    """简化的Kaiopetennki工作流程测试器"""
    
    def __init__(self):
        self.page = None
        self.selector_executor = None
        self.form_engine = None

    async def run_test(self):
        """运行简化的工作流程测试"""
        try:
            logger.info("🚀 开始简化的Kaiopetennki工作流程测试")
            
            # 1. 初始化浏览器
            await self._initialize_browser()
            
            # 2. 登录Kaipoke
            await self._login_kaipoke()
            
            # 3. 导航到目标页面
            await self._navigate_to_target_page()
            
            # 4. 测试简化的新規追加流程
            await self._test_simplified_add_button_flow()
            
            # 5. 测试表单字段可见性
            await self._test_form_field_visibility()
            
            logger.info("✅ 简化的工作流程测试完成")
            
        except Exception as e:
            logger.error(f"❌ 测试失败: {e}", exc_info=True)
            raise
        finally:
            await self._cleanup()

    async def _initialize_browser(self):
        """初始化浏览器"""
        logger.info("🔧 初始化浏览器...")
        await browser_manager.start_browser(headless=False)
        self.page = await browser_manager.get_page()
        self.selector_executor = SelectorExecutor(self.page)
        await self.selector_executor.initialize_mcp_fallback()
        
        # 初始化表单引擎（使用简化版本）
        from core.rpa_tools.tennki_form_engine import TennkiPerformanceMonitor
        performance_monitor = TennkiPerformanceMonitor()
        self.form_engine = TennkiFormEngine(self.selector_executor, performance_monitor)
        
        logger.info("✅ 浏览器初始化完成")

    async def _login_kaipoke(self):
        """登录Kaipoke"""
        logger.info("🔐 登录Kaipoke...")
        
        login_success = await kaipoke_login_with_env(
            self.page,
            'KAIPOKE_CORPORATION_ID',
            'KAIPOKE_MEMBER_LOGIN_ID', 
            'KAIPOKE_PASSWORD',
            'https://r.kaipoke.biz/kaipokebiz/login/COM020102.do'
        )
        
        if not login_success:
            raise Exception("Kaipoke登录失败")
            
        logger.info("✅ Kaipoke登录成功")

    async def _navigate_to_target_page(self):
        """导航到目标页面"""
        logger.info("🧭 导航到目标页面...")
        
        # 点击主菜单
        await self.selector_executor.smart_click(
            workflow="kaipoke_tennki",
            category="navigation", 
            element="main_menu",
            target_text="レセプト"
        )
        
        await self.page.wait_for_load_state("load")
        
        # 选择据点（使用测试据点）
        await self.page.click('text="訪問看護/4660190861"')
        await self.page.wait_for_load_state("load")
        
        # 导航到訪問看護页面
        await self.page.evaluate("""
            () => {
                const menuItem = document.querySelector('.dropdown:nth-child(3) li:nth-of-type(2) a');
                if (menuItem) menuItem.click();
            }
        """)
        
        await self.page.wait_for_load_state("load")
        
        # 选择月份（使用当前月份）
        try:
            await self.page.select_option('#selectServiceOfferYm', index=1)
        except Exception as e:
            logger.warning(f"月份选择失败: {e}")
        
        await self.page.wait_for_load_state("load")
        logger.info("✅ 成功导航到目标页面")

    async def _test_simplified_add_button_flow(self):
        """测试简化的新規追加按钮流程"""
        logger.info("🧪 测试简化的新規追加按钮流程...")
        
        # 使用简化的点击函数
        await self.form_engine._click_add_button()
        
        logger.info("✅ 新規追加按钮点击测试成功")

    async def _test_form_field_visibility(self):
        """测试表单字段可见性"""
        logger.info("🔍 测试表单字段可见性...")
        
        # 检查表单模态框是否可见
        modal_visible = await self.page.locator('#registModal').is_visible()
        if not modal_visible:
            raise Exception("表单模态框不可见")
        
        # 检查保险选择器是否可见
        insurance_visible = await self.page.locator('#inPopupInsuranceDivision01, #inPopupInsuranceDivision02').count()
        if insurance_visible == 0:
            raise Exception("保险选择器不可见")
        
        logger.info("✅ 表单字段可见性测试通过")

    async def _cleanup(self):
        """清理资源"""
        try:
            await browser_manager.close()
        except:
            pass


async def main():
    """主函数"""
    tester = SimplifiedKaiopetennkiTester()
    await tester.run_test()


if __name__ == '__main__':
    asyncio.run(main())
