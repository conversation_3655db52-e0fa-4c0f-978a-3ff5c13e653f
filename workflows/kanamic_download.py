"""
Kanamic Download Workflow
カナミックシステムにログインし、指定された各事業所の月次データをダウンロードし、
指定のGoogle DriveとSheetに保存する
"""
import os
import asyncio
from dotenv import load_dotenv
from logger_config import logger
from core.browser.browser_manager import browser_manager
from core.gsuite.drive_client import DriveClient
from agents.web_operator_agent import website_operator
from crewai import Task, Crew
from core.selector_executor import SelectorExecutor

# .envファイルから環境変数を読み込む
load_dotenv()

def run(config: dict):
    """カナミックダウンロードワークフローを実行します。"""
    asyncio.run(main_async(config))

async def main_async(config: dict):
    """メインの非同期実行関数"""
    logger.info("カナミックダウンロードワークフローを開始します。")
    
    workflow_config = config.get('config', {})
    login_url = workflow_config.get('login_url', 'https://portal.kanamic.net/tritrus/index/')
    account = workflow_config.get('account')
    password_env = workflow_config.get('password_env', 'KANAMIC_PASSWORD')
    gdrive_folder_id = workflow_config.get('gdrive_folder_id')
    download_path = workflow_config.get('download_path', '/tmp/kanamic_downloads')
    tasks = config.get('tasks', [])
    
    # 環境変数から認証情報を取得
    password = os.getenv(password_env)
    
    if not all([account, password, gdrive_folder_id]):
        logger.error("必要な設定が不足しています。アカウント、パスワード、Google DriveフォルダIDを確認してください。")
        return
    
    logger.info(f"ログイン情報: {account}")
    logger.info(f"Google DriveフォルダID: {gdrive_folder_id}")
    logger.info(f"実行タスク数: {len(tasks)}")
    
    # Google Drive クライアントを初期化
    try:
        drive_client = DriveClient()
        logger.info("Google Drive クライアントが正常に初期化されました。")
    except Exception as e:
        logger.error(f"Google Drive クライアントの初期化に失敗しました: {e}")
        return
    
    # ダウンロードディレクトリを作成
    os.makedirs(download_path, exist_ok=True)
    
    try:
        # ブラウザを起動
        await browser_manager.start_browser(headless=False)
        
        logger.info("ブラウザを起動しました。")

        # ページを取得してログイン
        page = await browser_manager.get_page()
        await page.goto(login_url, wait_until='networkidle', timeout=60000)
        logger.info(f"ページにアクセスしました: {login_url}")

        # セレクタ実行器を初期化
        selector_executor = SelectorExecutor(page)
        
        # 🆕 MCPバックアップツールを初期化
        await selector_executor.initialize_mcp_fallback()

        # ログイン処理（セレクタ優先 + MCPバックアップ）
        login_success = await login_to_kanamic_with_enhanced_selectors(selector_executor, account, password)

        if login_success:
            logger.info("✅ カナミックへのログインが完了しました（MCP強化版）。")

            # ログイン後の待機時間を追加（重要！）
            await page.wait_for_timeout(3000)
            logger.info("ログイン後の待機完了")

            # 各タスクを実行（MCP強化版）
            for task_config in tasks:
                await execute_kanamic_task_enhanced(selector_executor, task_config, download_path, drive_client, gdrive_folder_id)
        else:
            logger.warning("⚠️ セレクタとMCPが失敗、Agentフォールバックを実行")
            await call_agent_for_login(page, account, password)

    except Exception as e:
        logger.error(f"ワークフローの実行中にエラーが発生しました: {e}", exc_info=True)
    finally:
        # ブラウザを終了
        await browser_manager.close_browser()
        logger.info("ワークフローが終了しました。")

async def login_to_kanamic_with_enhanced_selectors(selector_executor, account, password):
    """カナミックにログインする（セレクタ優先 + MCPバックアップ版本）"""
    try:
        logger.info("カナミックにログインします（MCP強化版）...")

        # 強化されたセレクタ実行器を使用してログイン（MCPバックアップ含む）
        login_success = await selector_executor.execute_kanamic_login(account, password)

        if login_success:
            logger.info("✅ カナミックへのログインが完了しました（MCP強化版）。")
            return True
        else:
            logger.warning("⚠️ セレクタとMCPバックアップでのログインに失敗")
            return False

    except Exception as e:
        logger.error(f"MCP強化ログイン処理中にエラーが発生しました: {e}")
        return False

async def login_to_kanamic_with_selectors_old(selector_executor, account, password):
    """カナミックにログインする（选择器优先版本）"""
    try:
        logger.info("カナミックにログインします（选择器优先）...")
        
        # 选择器执行器を使用してログイン
        login_success = await selector_executor.execute_kanamic_login(account, password)
        
        if login_success:
            logger.info("✅ カナミックへのログインが完了しました（选择器優先）。")
            return True
        else:
            logger.warning("⚠️ 选择器でのログインに失敗")
            return False
        
    except Exception as e:
        logger.error(f"ログイン処理でエラーが発生しました: {e}")
        return False

async def execute_kanamic_task(selector_executor, task_config, download_path, drive_client, gdrive_folder_id):
    """個別のカナミックタスクを実行する"""
    task_id = task_config.get('task_id')
    target = task_config.get('target')
    params = task_config.get('params', {})
    
    logger.info(f"--- タスク開始: {task_id} ---")
    logger.info(f"目標: {target}")
    
    try:
        # 首先检查是否是债权任务
        main_menu_target = params.get('main_menu_target')
        if main_menu_target == "債権・会計":
            logger.info("检测到债权任务，调用专门的债权下载函数")
            download_success = await execute_debt_data_download_task(selector_executor, params, download_path, drive_client, gdrive_folder_id)
        # 检查是否为分步下载流程
        elif params.get('download_workflow') == "SEQUENTIAL_CHECKBOX_DOWNLOAD":
            download_success = await execute_sequential_checkbox_download(selector_executor, params, download_path, drive_client, gdrive_folder_id)
        else:
            # 传统的单次下载流程
            download_success = await execute_kanamic_download_task(selector_executor, params, download_path)

        if download_success:
            logger.info(f"✅ タスク '{task_id}' 完了")
        else:
            logger.warning(f"⚠️ タスク '{task_id}': 选择器でのダウンロードに失敗、Agentフォールバックを実行")
            await call_agent_for_kanamic_download(task_config, gdrive_folder_id)

    except Exception as e:
        logger.error(f"タスク '{task_id}' の実行中にエラーが発生しました: {e}")

async def execute_kanamic_download_task(selector_executor, params, download_path):
    """カナミックダウンロードタスクを実行する（选择器优先・改良版）"""
    try:
        # パラメータから設定を取得
        main_menu_target = params.get('main_menu_target')
        report_menu_target = params.get('report_menu_target')
        report_type_target = params.get('report_type_target')
        checkbox_selectors = params.get('checkbox_selectors', [])
        download_button_target = params.get('download_button_target')

        logger.info(f"メニューナビゲーション: {main_menu_target} -> {report_menu_target}")

        # 1. メインメニューをクリック（选择器优先・動的選択）
        if main_menu_target:
            success = await execute_dynamic_menu_click(selector_executor, "main_menu", main_menu_target)
            if success:
                await selector_executor.page.wait_for_timeout(2000)
                logger.info(f"✅ メインメニューをクリック: {main_menu_target}")
            else:
                logger.error(f"メインメニュークリック完全失敗: {main_menu_target}")
                return False

        # 2. レポートメニューをクリック（选择器优先・動的選択）
        if report_menu_target:
            success = await execute_dynamic_menu_click(selector_executor, "report_menu", report_menu_target)
            if success:
                await selector_executor.page.wait_for_timeout(3000)
                logger.info(f"✅ レポートメニューをクリック: {report_menu_target}")
            else:
                logger.error(f"レポートメニュークリック完全失敗: {report_menu_target}")
                return False

        # 3. サービスタイプを選択（选择器优先・改良版）
        if report_type_target:
            success = await execute_service_type_selection(selector_executor, report_type_target)
            if success:
                logger.info(f"✅ サービスタイプを選択: {report_type_target}")
            else:
                logger.warning(f"⚠️ サービスタイプ選択失敗: {report_type_target}")

        # 4. チェックボックス選択（选择器优先・改良版）
        await execute_checkbox_selections(selector_executor, checkbox_selectors)

        # 5. 前月選択のJavaScript実行（改良版）
        date_handling_method = params.get('date_handling_method')
        if date_handling_method == "SELECT_PREVIOUS_MONTH_JS":
            success = await execute_enhanced_previous_month_selection(selector_executor.page)
            if success:
                logger.info("✅ 前月選択完了")
            else:
                logger.warning("⚠️ 前月選択失敗")

        # 6. 検索ボタンをクリック（新規追加）
        search_success = await selector_executor.smart_click("kanamic", "download", "search_button")
        if search_success:
            logger.info("✅ 検索ボタンをクリック")
            await selector_executor.page.wait_for_timeout(3000)  # 検索結果待機
        else:
            logger.warning("⚠️ 検索ボタンクリック失敗")

        # 7. ダウンロードボタンをクリック（选择器优先・改良版）
        if download_button_target:
            return await execute_download_with_enhanced_selectors(
                selector_executor, download_button_target, params, download_path
            )

        return False

    except Exception as e:
        logger.error(f"カナミックダウンロードタスク実行失敗: {e}")
        return False

async def execute_previous_month_selection(page):
    """前月選択のJavaScriptを実行"""
    js_code = """
    (function() {
        var today = new Date();
        var lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        var year = lastMonth.getFullYear();
        var month = lastMonth.getMonth() + 1;
        
        // 年の選択
        var yearSelect = document.querySelector('select[name="year"]');
        if (yearSelect) {
            yearSelect.value = year.toString();
            var evt = new Event("change", { bubbles: true });
            yearSelect.dispatchEvent(evt);
        }
        
        // 月の選択
        var monthSelect = document.querySelector('select[name="month"]');
        if (monthSelect) {
            var monthStr = month < 10 ? "0" + month : month.toString();
            monthSelect.value = monthStr;
            var evt = new Event("change", { bubbles: true });
            monthSelect.dispatchEvent(evt);
        }
        
        console.log("前月を選択しました:", year + "年" + month + "月");
        return true;
    })()
    """
    
    result = await page.evaluate(js_code)
    logger.info("前月選択のJavaScriptを実行しました")
    return result

async def execute_service_type_selection(selector_executor, report_type_target):
    """サービスタイプ選択を実行（选择器优先・改良版）"""
    try:
        # 1. サービスタイプドロップダウンを選択
        dropdown_success = await selector_executor.smart_select_option(
            "kanamic", "download", "service_type_dropdown", value="CONTRACT_FLAG999"
        )

        if dropdown_success:
            logger.info("✅ サービスタイプドロップダウン選択成功")
            await selector_executor.page.wait_for_timeout(1000)
            return True
        else:
            # フォールバック: 直接オプションをクリック
            try:
                await selector_executor.page.click(f'option:contains("{report_type_target}")', timeout=5000)
                logger.info(f"✅ フォールバック: サービスタイプオプション選択成功: {report_type_target}")
                return True
            except Exception as e:
                logger.warning(f"サービスタイプ選択フォールバック失敗: {e}")
                return False

    except Exception as e:
        logger.error(f"サービスタイプ選択エラー: {e}")
        return False

async def execute_checkbox_selections(selector_executor, checkbox_selectors):
    """チェックボックス選択を実行（选择器优先・改良版）"""
    success_count = 0

    for i, checkbox_selector in enumerate(checkbox_selectors):
        try:
            # 直接セレクタでチェック
            await selector_executor.page.check(checkbox_selector, timeout=8000)
            logger.info(f"✅ チェックボックス選択成功: {checkbox_selector}")
            success_count += 1
        except Exception as e:
            logger.warning(f"⚠️ チェックボックス選択失敗 {checkbox_selector}: {e}")

            # フォールバック: 個別の选择器を試行
            fallback_selectors = [
                f"#checkItem{i}",
                f"input[id='checkItem{i}']",
                f"input[name='checkItem{i}']"
            ]

            for fallback_selector in fallback_selectors:
                try:
                    await selector_executor.page.check(fallback_selector, timeout=3000)
                    logger.info(f"✅ フォールバック成功: {fallback_selector}")
                    success_count += 1
                    break
                except Exception:
                    continue

    logger.info(f"チェックボックス選択完了: {success_count}/{len(checkbox_selectors)} 成功")
    return success_count > 0

async def execute_enhanced_previous_month_selection(page):
    """前月選択のJavaScript実行（改良版）"""
    js_code = """
    (function() {
        try {
            var today = new Date();
            var lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
            var year = lastMonth.getFullYear();
            var month = lastMonth.getMonth() + 1;

            var result = { year: year, month: month, success: false };

            // 年の選択（複数のセレクタを試行）
            var yearSelectors = [
                '#serviceyearkey',
                'select[name="serviceyearkey"]',
                'select[name="year"]',
                '#year'
            ];

            var yearSelect = null;
            for (var i = 0; i < yearSelectors.length; i++) {
                yearSelect = document.querySelector(yearSelectors[i]);
                if (yearSelect) break;
            }

            if (yearSelect) {
                yearSelect.value = year.toString();
                var evt = new Event("change", { bubbles: true });
                yearSelect.dispatchEvent(evt);
                result.yearSelected = true;
            }

            // 月の選択（複数のセレクタを試行）
            var monthSelectors = [
                '#servicemonthkey',
                'select[name="servicemonthkey"]',
                'select[name="month"]',
                '#month'
            ];

            var monthSelect = null;
            for (var i = 0; i < monthSelectors.length; i++) {
                monthSelect = document.querySelector(monthSelectors[i]);
                if (monthSelect) break;
            }

            if (monthSelect) {
                var monthStr = month < 10 ? "0" + month : month.toString();
                monthSelect.value = monthStr;
                var evt = new Event("change", { bubbles: true });
                monthSelect.dispatchEvent(evt);
                result.monthSelected = true;
            }

            result.success = result.yearSelected && result.monthSelected;
            return result;

        } catch (error) {
            return { success: false, error: error.message };
        }
    })();
    """

    try:
        result = await page.evaluate(js_code)
        if result.get('success'):
            logger.info(f"✅ 前月選択成功: {result.get('year')}年{result.get('month')}月")
            return True
        else:
            logger.warning(f"⚠️ 前月選択失敗: {result}")
            return False
    except Exception as e:
        logger.error(f"前月選択JavaScript実行エラー: {e}")
        return False

async def execute_download_with_enhanced_selectors(selector_executor, download_button_target, params, download_path):
    """ダウンロード実行（选择器优先・改良版）"""
    try:
        # 1. 选择器优先でダウンロードボタンをクリック
        download_success = await selector_executor.smart_click("kanamic", "download", "download_button")

        if not download_success:
            # フォールバック: 直接テキスト選択
            logger.warning("选择器でダウンロードボタンクリック失敗、フォールバック実行")
            try:
                await selector_executor.page.click(f'text="{download_button_target}"', timeout=10000)
                download_success = True
            except Exception as e:
                logger.error(f"ダウンロードボタンフォールバック失敗: {e}")
                return False

        if download_success:
            logger.info("✅ ダウンロードボタンクリック成功")

            # 2. ダウンロード待機とファイル保存
            try:
                async with selector_executor.page.expect_download(timeout=60000) as download_info:
                    pass  # ダウンロード開始を待機

                download = await download_info.value
                file_name_template = params.get('file_name_template', 'kanamic_download_{datetime}.csv')

                # ファイル名生成
                from datetime import datetime
                template_vars = {
                    'datetime': datetime.now().strftime('%Y%m%d_%H%M%S'),
                    'year': datetime.now().year,
                    'month': datetime.now().month
                }
                filename = file_name_template.format(**template_vars)

                save_path = os.path.join(download_path, filename)
                await download.save_as(save_path)

                logger.info(f"✅ ファイルダウンロード成功: {save_path}")
                return True

            except Exception as e:
                logger.error(f"ダウンロード処理失敗: {e}")
                return False

        return False

    except Exception as e:
        logger.error(f"ダウンロード実行エラー: {e}")
        return False

async def call_agent_for_login(page, account, password):
    """セレクタ失効時にAgentを呼び出してログイン処理を行う"""
    logger.info("セレクタが失効したため、Agentを呼び出します。")
    
    task = Task(
        description=f"カナミックのログインページで、アカウント '{account}' とパスワードを使用してログインしてください。ログインフォームを見つけて入力し、ログインボタンをクリックしてください。",
        agent=website_operator,
        expected_output="ログインが成功し、ダッシュボードまたはメインページに遷移していること。"
    )
    
    crew = Crew(
        agents=[website_operator],
        tasks=[task],
        verbose=True
    )
    
    try:
        result = crew.kickoff()
        logger.info(f"Agentによるログイン処理が完了しました: {result}")
    except Exception as e:
        logger.error(f"Agentによるログイン処理でエラーが発生しました: {e}")

async def call_agent_for_kanamic_download(task_config, gdrive_folder_id):
    """セレクタ失効時にAgentを呼び出してカナミックダウンロード処理を行う"""
    logger.info("セレクタが失効したため、Agentでカナミックダウンロード処理を実行します。")
    
    task_id = task_config.get('task_id')
    target = task_config.get('target')
    params = task_config.get('params', {})
    
    task = Task(
        description=f"""
        カナミックのシステムで以下の手順でデータをダウンロードしてください：
        1. メニュー「{params.get('main_menu_target')}」をクリック
        2. サブメニュー「{params.get('report_menu_target')}」をクリック
        3. 必要に応じてレポートタイプを選択
        4. 指定されたチェックボックスを選択
        5. 前月を選択
        6. ダウンロードボタンをクリックしてファイルをダウンロード
        7. ダウンロードしたファイルをGoogle Driveにアップロード
        
        タスクID: {task_id}
        目標: {target}
        """,
        agent=website_operator,
        expected_output="データファイルが正常にダウンロードされ、Google Driveの指定フォルダにアップロードされていること。"
    )
    
    crew = Crew(
        agents=[website_operator],
        tasks=[task],
        verbose=True
    )
    
    try:
        result = crew.kickoff()
        logger.info(f"Agentによるカナミックダウンロード処理が完了しました: {result}")
    except Exception as e:
        logger.error(f"Agentによるカナミックダウンロード処理でエラーが発生しました: {e}")

async def execute_kanamic_task_enhanced(selector_executor, task_config, download_path, drive_client, gdrive_folder_id):
    """個別のカナミックタスクを実行する（MCP強化版）"""
    task_id = task_config.get('task_id')
    target = task_config.get('target')
    params = task_config.get('params', {})
    
    logger.info(f"--- MCP強化タスク開始: {task_id} ---")
    logger.info(f"目標: {target}")
    
    try:
        # まず債権タスクかどうかをチェック
        main_menu_target = params.get('main_menu_target')
        if main_menu_target == "債権・会計":
            logger.info("債権タスクを検出、既存の債権ダウンロード関数を呼び出し（MCP強化セレクタ使用）")
            download_success = await execute_debt_data_download_task(selector_executor, params, download_path, drive_client, gdrive_folder_id)
        # 分歩ダウンロードフローかどうかをチェック
        elif params.get('download_workflow') == "SEQUENTIAL_CHECKBOX_DOWNLOAD":
            logger.info("分歩ダウンロードフローを実行（MCP強化セレクタ使用）")
            download_success = await execute_sequential_checkbox_download(selector_executor, params, download_path, drive_client, gdrive_folder_id)
        else:
            # 従来の単発ダウンロードフロー
            logger.info("標準ダウンロードフローを実行（MCP強化セレクタ使用）")
            download_success = await execute_kanamic_download_task(selector_executor, params, download_path)

        if download_success:
            logger.info(f"✅ MCP強化タスク '{task_id}' 完了")
        else:
            logger.warning(f"⚠️ タスク '{task_id}': セレクタとMCPでのダウンロードに失敗、Agentフォールバックを実行")
            await call_agent_for_kanamic_download(task_config, gdrive_folder_id)

    except Exception as e:
        logger.error(f"MCP強化タスク '{task_id}' の実行中にエラーが発生しました: {e}")

async def execute_dynamic_menu_click_enhanced(selector_executor, menu_type, target_text):
    """動的メニュークリック - MCP強化版（セレクタ優先 + MCPバックアップ）"""
    try:
        # 強化されたsmart_clickを使用（MCPバックアップ含む）
        success = await selector_executor.smart_click("kanamic", "download", menu_type, target_text=target_text)
        if success:
            logger.info(f"✅ MCP強化メニュー選択成功: {target_text}")
            return True
        
        # すべてのバックアップが失敗した場合
        logger.warning(f"⚠️ MCP強化メニュー選択失敗: {target_text}")
        return False
        
    except Exception as e:
        logger.error(f"MCP強化メニュークリックエラー: {e}")
        return False

async def execute_dynamic_menu_click(selector_executor, menu_type, target_text):
    """動的メニュークリック - 特定のテキストを優先して選択"""
    try:
        # 1. まず特定のテキストで直接試行
        try:
            await selector_executor.page.click(f'text="{target_text}"', timeout=8000)
            logger.info(f"✅ 直接テキスト選択成功: {target_text}")
            return True
        except Exception as e:
            logger.debug(f"直接テキスト選択失敗 {target_text}: {e}")

        # 2. 选择器优先で試行
        success = await selector_executor.smart_click("kanamic", "download", menu_type)
        if success:
            logger.info(f"✅ 选择器選択成功: {menu_type}")
            return True

        # 3. 追加のフォールバック選択器を試行
        fallback_selectors = [
            f'a:contains("{target_text}")',
            f'[href*="{target_text.lower()}"]',
            f'.menu-item:contains("{target_text}")',
            f'li a:contains("{target_text}")',
            f'nav a:contains("{target_text}")'
        ]

        for selector in fallback_selectors:
            try:
                await selector_executor.page.click(selector, timeout=5000)
                logger.info(f"✅ フォールバック選択器成功: {selector}")
                return True
            except Exception as e:
                logger.debug(f"フォールバック選択器失敗 {selector}: {e}")
                continue

        logger.warning(f"⚠️ 全ての選択器が失敗: {target_text}")
        return False

    except Exception as e:
        logger.error(f"動的メニュークリックエラー: {e}")
        return False

async def execute_sequential_checkbox_download(selector_executor, params, download_path, drive_client, gdrive_folder_id):
    """分步复选框下载流程 - 福岡居宅+居宅介護支援事業所专用"""
    try:
        # 获取基本参数
        main_menu_target = params.get('main_menu_target')
        report_menu_target = params.get('report_menu_target')
        report_type_target = params.get('report_type_target')
        sequential_downloads = params.get('sequential_downloads', [])

        logger.info(f"开始分步下载流程，共{len(sequential_downloads)}个步骤")

        # 1. 导航到报告页面
        logger.info(f"メニューナビゲーション: {main_menu_target} -> {report_menu_target}")

        # 主菜单点击
        if main_menu_target:
            success = await execute_dynamic_menu_click(selector_executor, "main_menu", main_menu_target)
            if success:
                await selector_executor.page.wait_for_timeout(2000)
                logger.info(f"✅ メインメニューをクリック: {main_menu_target}")
            else:
                logger.error(f"メインメニュークリック失敗: {main_menu_target}")
                return False

        # 报告子菜单点击
        if report_menu_target:
            success = await execute_dynamic_menu_click(selector_executor, "report_menu", report_menu_target)
            if success:
                await selector_executor.page.wait_for_timeout(3000)
                logger.info(f"✅ レポートメニューをクリック: {report_menu_target}")
            else:
                logger.error(f"レポートメニュークリック失敗: {report_menu_target}")
                return False

        # 2. 跳过服务类型选择（福冈居宅和居宅任务不需要）
        # 注释掉服务类型选择，直接进行日期选择
        # if report_type_target:
        #     success = await execute_service_type_selection(selector_executor, report_type_target)
        #     if success:
        #         logger.info(f"✅ サービスタイプを選択: {report_type_target}")
        #     else:
        #         logger.warning(f"⚠️ サービスタイプ選択失敗: {report_type_target}")

        # 3. 选择#targetMonth为上个月
        await select_target_month_previous(selector_executor.page)
        
        # 4. 点击#doSearch検索按钮
        search_success = await click_do_search_button(selector_executor.page)
        if search_success:
            logger.info("✅ #doSearch検索ボタンをクリック")
            await selector_executor.page.wait_for_timeout(3000)  # 等待搜索结果
        else:
            logger.warning("⚠️ #doSearch検索ボタンクリック失敗")

        # 5. 执行分步下载
        all_downloads_success = True
        for step_config in sequential_downloads:
            step_num = step_config.get('step')
            description = step_config.get('description')
            logger.info(f"--- 步骤 {step_num}: {description} ---")

            step_success = await execute_single_checkbox_download_step(
                selector_executor, step_config, download_path, drive_client, gdrive_folder_id
            )

            if not step_success:
                logger.error(f"步骤 {step_num} 失败")
                all_downloads_success = False
                break
            else:
                logger.info(f"✅ 步骤 {step_num} 完成")

        # 第一个任务完成后，返回主页面为第二个任务做准备
        if all_downloads_success:
            try:
                await selector_executor.page.goto("https://portal.kanamic.net/tritrus/", wait_until='networkidle', timeout=60000)
                logger.info("✅ 第一个任务完成，已返回主页面")
                await selector_executor.page.wait_for_timeout(2000)
            except Exception as e:
                logger.warning(f"返回主页面失败: {e}")
        
        return all_downloads_success

    except Exception as e:
        logger.error(f"分步下载流程执行失败: {e}")
        return False

async def execute_single_checkbox_download_step(selector_executor, step_config, download_path, drive_client, gdrive_folder_id):
    """执行单个复选框下载步骤 - 按照新的流程要求"""
    try:
        step_num = step_config.get('step')
        description = step_config.get('description')
        
        # 1. 取消勾选上一个复选框（如果需要）
        uncheck_previous = step_config.get('uncheck_previous')
        if uncheck_previous:
            try:
                await selector_executor.page.uncheck(uncheck_previous, timeout=5000)
                logger.info(f"✅ 取消勾选: {uncheck_previous}")
                await selector_executor.page.wait_for_timeout(1000)
            except Exception as e:
                logger.warning(f"取消勾选失败 {uncheck_previous}: {e}")

        # 2. 勾选当前复选框
        checkbox_selector = step_config.get('checkbox_selector')
        if checkbox_selector:
            try:
                await selector_executor.page.check(checkbox_selector, timeout=8000)
                logger.info(f"✅ 勾选复选框: {checkbox_selector}")
                await selector_executor.page.wait_for_timeout(1000)
            except Exception as e:
                logger.error(f"勾选复选框失败 {checkbox_selector}: {e}")
                return False

        # 3. 点击#doCsv-2下载按钮并下载文件
        download_button_selector = step_config.get('download_button_selector', '#doCsv-2')
        if download_button_selector:
            try:
                # 先点击下载按钮
                await selector_executor.page.click(download_button_selector, timeout=10000)
                logger.info(f"✅ 点击下载按钮: {download_button_selector}")
                
                # 等待页面响应
                await selector_executor.page.wait_for_timeout(2000)
                
                # 等待下载开始
                async with selector_executor.page.expect_download(timeout=30000) as download_info:
                    # 检查是否需要再次点击或确认
                    try:
                        # 如果页面有变化，可能需要再次点击
                        if await selector_executor.page.locator(download_button_selector).count() > 0:
                            await selector_executor.page.click(download_button_selector, timeout=5000)
                            logger.info(f"✅ 再次点击下载按钮: {download_button_selector}")
                    except:
                        logger.debug("再次点击失败，可能下载已开始")

                download = await download_info.value

                # 生成文件名 - 根据据点名称
                facility_name = "福岡居宅" if step_num == 1 else "居宅介護支援"
                from datetime import datetime
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                csv_filename = f"あおぞら介護ステーション{facility_name}_{timestamp}.csv"
                csv_path = os.path.join(download_path, csv_filename)

                await download.save_as(csv_path)
                logger.info(f"✅ CSV文件下载成功: {csv_path}")

                # 4. 使用data_tools处理CSV转换
                from agents.tools.data_tools import process_downloaded_csv
                xlsx_path = process_downloaded_csv(
                    download_path, 
                    step_config.get('file_name_template', f'あおぞら介護ステーション{facility_name}_{timestamp}.xlsx')
                )
                
                # 5. 上传到Google Drive
                if drive_client and gdrive_folder_id:
                    drive_file_id = drive_client.upload_file(xlsx_path, gdrive_folder_id)
                    if drive_file_id:
                        logger.info(f"✅ 文件上传到Google Drive成功: {drive_file_id}")
                    else:
                        logger.warning("⚠️ Google Drive上传失败")

                # 6. 使用sheets_client更新Google Sheets（福冈居宅任务：从第9行开始读取数据）
                await update_google_sheets_with_sheets_client(xlsx_path, step_config, data_start_row=1)

                # 7. 清理临时文件
                try:
                    os.remove(csv_path)
                    logger.info(f"清理CSV临时文件: {csv_path}")
                except Exception as e:
                    logger.warning(f"CSV文件清理失败: {e}")

                return True

            except Exception as e:
                logger.error(f"下载处理失败: {e}")
                return False

        return False

    except Exception as e:
        logger.error(f"单步下载执行失败: {e}")
        return False

async def process_downloaded_file_for_step(file_path, step_config, drive_client, gdrive_folder_id):
    """处理下载的文件并上传到对应的Google Sheets"""
    try:
        import pandas as pd
        from datetime import datetime

        # 1. 读取CSV文件
        df = pd.read_csv(file_path, encoding='shift_jis')
        logger.info(f"读取CSV文件成功，共{len(df)}行数据")

        # 2. 转换为XLSX格式
        xlsx_filename = file_path.replace('.csv', '.xlsx')
        df.to_excel(xlsx_filename, index=False, header=True)
        logger.info(f"转换为XLSX格式: {xlsx_filename}")

        # 3. 上传到Google Drive
        if drive_client and gdrive_folder_id:
            drive_file_id = drive_client.upload_file(xlsx_filename, gdrive_folder_id)
            if drive_file_id:
                logger.info(f"✅ 文件上传到Google Drive成功: {drive_file_id}")
            else:
                logger.warning("⚠️ Google Drive上传失败")

        # 4. 更新Google Sheets（如果配置了）
        target_sheet_id = step_config.get('target_sheet_id')
        if target_sheet_id:
            await update_google_sheets_with_data(df, step_config, xlsx_filename)

        # 5. 清理临时文件
        import os
        try:
            os.remove(file_path)
            os.remove(xlsx_filename)
            logger.info("临时文件清理完成")
        except Exception as e:
            logger.warning(f"临时文件清理失败: {e}")

    except Exception as e:
        logger.error(f"文件处理失败: {e}")

async def select_target_month_previous(page):
    """选择#targetMonth为上个月"""
    try:
        js_code = """
        (function() {
            try {
                var today = new Date();
                var lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                var month = lastMonth.getMonth() + 1;
                
                // 先选择年份
                var targetYearSelect = document.querySelector('#targetYear');
                if (targetYearSelect) {
                    var year = lastMonth.getFullYear();
                    targetYearSelect.value = year.toString();
                    targetYearSelect.dispatchEvent(new Event('change', { bubbles: true }));
                }
                
                // 再选择月份
                var targetMonthSelect = document.querySelector('#targetMonth');
                if (targetMonthSelect) {
                    var monthStr = month < 10 ? "0" + month : month.toString();
                    
                    // 直接设置月份值（01-12格式）
                    targetMonthSelect.value = monthStr;
                    targetMonthSelect.dispatchEvent(new Event('change', { bubbles: true }));
                    
                    // 验证是否设置成功
                    if (targetMonthSelect.value === monthStr) {
                        return { 
                            success: true, 
                            year: year,
                            month: monthStr,
                            monthText: targetMonthSelect.options[targetMonthSelect.selectedIndex].text
                        };
                    } else {
                        return { success: false, error: "月份设置失败" };
                    }
                } else {
                    return { success: false, error: "targetMonth select not found" };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        })();
        """
        
        result = await page.evaluate(js_code)
        if result.get('success'):
            logger.info(f"✅ #targetMonth选择上个月成功: {result.get('year')}年{result.get('month')}月")
            return True
        else:
            logger.warning(f"⚠️ #targetMonth选择失败: {result.get('error')}")
            return False
            
    except Exception as e:
        logger.error(f"#targetMonth选择出错: {e}")
        return False

async def click_do_search_button(page):
    """点击#doSearch検索按钮"""
    try:
        await page.click('#doSearch', timeout=10000)
        logger.info("✅ #doSearch按钮点击成功")
        return True
    except Exception as e:
        logger.error(f"#doSearch按钮点击失败: {e}")
        return False

async def convert_csv_to_xlsx_with_date_format(csv_path, step_config):
    """将CSV转换为XLSX格式，处理日期格式"""
    try:
        import pandas as pd
        from datetime import datetime
        import re
        
        # 尝试多种方法读取CSV文件
        df = None
        
        # 方法1: 尝试不同编码
        encodings = ['shift_jis', 'utf-8', 'cp932', 'euc-jp', 'iso-2022-jp']
        for encoding in encodings:
            try:
                df = pd.read_csv(csv_path, encoding=encoding, on_bad_lines='skip')
                logger.info(f"使用编码 {encoding} 读取CSV文件成功，共{len(df)}行数据")
                break
            except Exception as e:
                logger.debug(f"编码 {encoding} 读取失败: {e}")
                continue
        
        # 方法2: 如果编码失败，尝试错误处理
        if df is None:
            for encoding in ['shift_jis', 'cp932', 'utf-8']:
                try:
                    df = pd.read_csv(csv_path, encoding=encoding, errors='ignore', on_bad_lines='skip')
                    logger.info(f"使用编码 {encoding} (忽略错误) 读取CSV文件成功，共{len(df)}行数据")
                    break
                except Exception as e:
                    logger.debug(f"编码 {encoding} (忽略错误) 读取失败: {e}")
                    continue
        
        # 方法3: 如果还是失败，尝试二进制读取并清理
        if df is None:
            try:
                # 读取原始字节并清理
                with open(csv_path, 'rb') as f:
                    raw_data = f.read()
                
                # 尝试检测编码
                import chardet
                detected = chardet.detect(raw_data)
                detected_encoding = detected.get('encoding', 'shift_jis')
                logger.info(f"检测到编码: {detected_encoding}")
                
                # 使用检测到的编码读取
                df = pd.read_csv(csv_path, encoding=detected_encoding, errors='ignore', on_bad_lines='skip')
                logger.info(f"使用检测编码 {detected_encoding} 读取CSV文件成功，共{len(df)}行数据")
                
            except Exception as e:
                logger.error(f"二进制读取也失败: {e}")
                # 最后尝试：直接复制文件作为XLSX
                try:
                    import shutil
                    xlsx_path = csv_path.replace('.csv', '.xlsx')
                    shutil.copy2(csv_path, xlsx_path)
                    logger.warning(f"无法转换CSV，直接复制文件: {xlsx_path}")
                    return xlsx_path
                except Exception as e2:
                    logger.error(f"文件复制也失败: {e2}")
                    return csv_path
        
        if df is None:
            logger.error("所有方法都无法读取CSV文件")
            return csv_path
        
        # 生成XLSX文件名
        xlsx_path = csv_path.replace('.csv', '.xlsx')
        
        # 保存为XLSX格式
        with pd.ExcelWriter(xlsx_path, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, header=True)
        logger.info(f"✅ 转换为XLSX格式成功: {xlsx_path}")
        
        return xlsx_path
        
    except Exception as e:
        logger.error(f"CSV转XLSX转换失败: {e}")
        return csv_path

async def update_google_sheets_with_sheets_client(xlsx_path, step_config, data_start_row=1):
    """使用sheets_client更新Google Sheets数据"""
    try:
        from core.gsuite.sheets_client import SheetsClient
        import pandas as pd
        import os
        
        target_sheet_id = step_config.get('target_sheet_id')
        if not target_sheet_id:
            logger.warning("未找到target_sheet_id，跳过Google Sheets更新")
            return
        
        # 读取Excel文件，跳过前面的行，从指定行开始读取数据
        df = pd.read_excel(xlsx_path, engine='openpyxl', skiprows=data_start_row-1)
        logger.info(f"从第{data_start_row}行开始读取数据，共{len(df)}行")
        
        # 创建临时文件，只包含需要的数据
        temp_xlsx_path = xlsx_path.replace('.xlsx', f'_data_only.xlsx')
        df.to_excel(temp_xlsx_path, index=False, engine='openpyxl')
        
        # 构建数据处理规则
        data_processing_rules = step_config.get('data_processing_rules', [])
        if not data_processing_rules:
            # 如果没有规则，创建默认规则
            data_processing_rules = [{
                'target_sheet_id': target_sheet_id,
                'header_row': 1,  # 临时文件的表头在第1行
                'paste_start_row': step_config.get('paste_start_row', 5),
                'pre_paste_updates': step_config.get('pre_paste_updates', [])
            }]
        else:
            # 更新规则，因为现在使用的是处理过的临时文件
            for rule in data_processing_rules:
                rule['header_row'] = 1  # 临时文件的表头在第1行
        
        # 使用SheetsClient处理数据
        sheets_client = SheetsClient()
        sheets_client.process_and_write_data(temp_xlsx_path, data_processing_rules)
        
        # 清理临时文件
        try:
            os.remove(temp_xlsx_path)
            logger.debug(f"清理临时文件: {temp_xlsx_path}")
        except Exception as e:
            logger.warning(f"临时文件清理失败: {e}")
        
        logger.info(f"✅ 使用SheetsClient更新Google Sheets成功: {target_sheet_id} (数据从第{data_start_row}行开始)")
        
    except Exception as e:
        logger.error(f"SheetsClient更新失败: {e}", exc_info=True)

def convert_date_format(date_string):
    """将日期格式从'2025年07月08日 13:46'转换为'2025/05/01'格式"""
    try:
        import re
        from datetime import datetime
        
        # 匹配日期格式：2025年07月08日 13:46
        pattern = r'(\d{4})年(\d{1,2})月(\d{1,2})日'
        match = re.search(pattern, str(date_string))
        
        if match:
            year = match.group(1)
            month = match.group(2).zfill(2)  # 补零
            day = match.group(3).zfill(2)    # 补零
            
            formatted_date = f"{year}/{month}/{day}"
            return formatted_date
        else:
            logger.warning(f"日期格式不匹配: {date_string}")
            return str(date_string)
            
    except Exception as e:
        logger.error(f"日期格式转换失败: {e}")
        return str(date_string)

async def execute_debt_data_download_task(selector_executor, params, download_path, drive_client, gdrive_folder_id):
    """执行债权数据下载 - 第三个任务"""
    try:
        # 1. 确保在主页面（第一个任务完成后应该已经在主页面）
        current_url = selector_executor.page.url
        if "portal.kanamic.net/tritrus" not in current_url:
            await selector_executor.page.goto("https://portal.kanamic.net/tritrus/", wait_until='networkidle', timeout=60000)
            logger.info("✅ 跳转到主页面")
            await selector_executor.page.wait_for_timeout(2000)
        else:
            logger.info("✅ 已在主页面，开始债权任务")
            await selector_executor.page.wait_for_timeout(1000)

        # 2. 点击债权・会计菜单
        try:
            # 首先尝试精确的选择器
            await selector_executor.page.click('a:nth-of-type(25) .btn', timeout=10000)
            logger.info("✅ 点击债权・会计菜单")
            await selector_executor.page.wait_for_timeout(2000)
        except Exception as e:
            logger.warning(f"主选择器失败: {e}")
            # 备用选择器列表
            backup_selectors = [
                'text="債権・会計"',
                'a:contains("債権・会計")',
                '[href*="saiken"]',
                '.btn:contains("債権・会計")',
                'a[title*="債権"]'
            ]
            
            success = False
            for selector in backup_selectors:
                try:
                    await selector_executor.page.click(selector, timeout=5000)
                    logger.info(f"✅ 点击债权・会计菜单成功（备用选择器: {selector}）")
                    await selector_executor.page.wait_for_timeout(2000)
                    success = True
                    break
                except Exception as e2:
                    logger.debug(f"备用选择器失败 {selector}: {e2}")
                    continue
            
            if not success:
                logger.error("所有债权・会计菜单选择器都失败")
                return False

        # 3. 点击债权管理
        try:
            await selector_executor.page.click('#isA17t123-1 li:nth-of-type(2) .saikenmenu1', timeout=10000)
            logger.info("✅ 点击债权管理菜单")
            await selector_executor.page.wait_for_timeout(3000)
        except Exception as e:
            # 备用选择器
            await selector_executor.page.click('text="債権管理"', timeout=10000)
            logger.info("✅ 点击债权管理菜单（备用选择器）")
            await selector_executor.page.wait_for_timeout(3000)

        # 4. 设置年份和月份
        await set_debt_year_and_month(selector_executor.page)

        # 5. 直接点击#doCsv下载（债权管理不需要検索步骤）
        try:
            # 先点击按钮
            await selector_executor.page.click('#doCsv', timeout=10000)
            logger.info("✅ 点击#doCsv债权一览CSV输出按钮")
            
            # 等待可能的弹窗或页面响应
            await selector_executor.page.wait_for_timeout(3000)
            
            # 检查是否有确认弹窗
            try:
                # 查找可能的确认按钮
                confirm_selectors = [
                    'button:has-text("OK")',
                    'button:has-text("確認")', 
                    'button:has-text("はい")',
                    'input[type="button"][value*="OK"]',
                    'input[type="button"][value*="確認"]'
                ]
                
                for selector in confirm_selectors:
                    try:
                        if await selector_executor.page.locator(selector).count() > 0:
                            await selector_executor.page.click(selector, timeout=5000)
                            logger.info(f"✅ 点击确认按钮: {selector}")
                            await selector_executor.page.wait_for_timeout(2000)
                            break
                    except:
                        continue
                        
            except Exception as e:
                logger.debug(f"确认按钮检查: {e}")
            
            # 现在等待下载
            async with selector_executor.page.expect_download(timeout=30000) as download_info:
                # 如果还没有下载，再次点击下载按钮
                try:
                    await selector_executor.page.click('#doCsv', timeout=5000)
                    logger.info("✅ 再次点击#doCsv按钮")
                except:
                    logger.debug("再次点击失败，可能下载已开始")

            download = await download_info.value

            # 生成文件名
            from datetime import datetime, timedelta
            today = datetime.now()
            last_month = today.replace(day=1) - timedelta(days=1)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            csv_filename = f"梅ヶ丘債権データ特養・短期_{last_month.strftime('%Y年%m月')}実績_{timestamp}.csv"
            csv_path = os.path.join(download_path, csv_filename)

            await download.save_as(csv_path)
            logger.info(f"✅ 债权CSV文件下载成功: {csv_path}")
            
            # 等待文件完全写入
            await selector_executor.page.wait_for_timeout(2000)

            # 6. 使用data_tools处理債権CSV転換（梅ヶ丘債権データ専用設定）
            from agents.tools.data_tools import process_downloaded_csv
            file_name_template = params.get('file_name_template', f'梅ヶ丘債権データ特養・短期_{last_month.strftime("%Y年%m月")}実績_{timestamp}.xlsx')
            # 梅ヶ丘債権データ：第1行表頭、第2行開始データ
            xlsx_path = process_downloaded_csv(download_path, file_name_template, data_start_row=2)

            # 7. 上传到Google Drive
            target_drive_folder_id = params.get('target_drive_folder_id') or gdrive_folder_id
            if drive_client and target_drive_folder_id:
                drive_file_id = drive_client.upload_file(xlsx_path, target_drive_folder_id)
                if drive_file_id:
                    logger.info(f"✅ 债权文件上传到Google Drive成功: {drive_file_id}")
                else:
                    logger.warning("⚠️ 债权文件Google Drive上传失败")

            # 8. 使用sheets_client更新1690表（债权任务：从第2行开始读取数据）
            await update_debt_sheets_with_client(xlsx_path, params, data_start_row=1)

            

            return True

        except Exception as e:
            logger.error(f"债权CSV下载失败: {e}")
            return False

    except Exception as e:
        logger.error(f"债权数据下载执行失败: {e}")
        return False

async def set_debt_year_and_month(page):
    """设置债权管理的年份和月份"""
    try:
        # 1. 注入JavaScript获取当前年月
        js_code_1 = """
        (() => {
            // 获取当前日期
            var today = new Date();
            // 获取上个月的日期对象
            var lastMonthDate = new Date(today);
            lastMonthDate.setMonth(today.getMonth() - 1);

            // 获取年份 (四位数字)
            var year = lastMonthDate.getFullYear();

            // 获取月份 (数字 1-12)
            var month = lastMonthDate.getMonth() + 1; // getMonth() 返回 0-11, 所以需要 +1

            // 将年份和月份分别存储到 window 对象上
            window.targetYear = year; // 例如：2025
            window.targetMonth = month; // 例如：5 (代表五月)
            
            return { year: year, month: month };
        })();
        """
        
        result = await page.evaluate(js_code_1)
        logger.info(f"✅ 获取目标年月: {result['year']}年{result['month']}月")

        # 2. 输入年份到#serviceyearkey
        try:
            await page.fill('#serviceyearkey', str(result['year']), timeout=10000)
            logger.info(f"✅ 输入年份: {result['year']}")
            await page.wait_for_timeout(1000)
        except Exception as e:
            logger.warning(f"年份输入失败: {e}")

        # 3. 选择月份 - 简化版本避免JavaScript错误
        try:
            # 先尝试直接选择
            month_str = str(result['month']).zfill(2)
            await page.select_option('#servicemonthkey', value=month_str)
            logger.info(f"✅ 直接选择月份成功: {result['month']}月")
        except Exception as e:
            logger.warning(f"直接选择失败，使用简化JavaScript: {e}")
            
            # 使用简化的JavaScript
            target_month = str(result['month'])
            js_code_2 = f"""
            (function() {{
                var selectElement = document.querySelector("#servicemonthkey");
                if (selectElement) {{
                    var options = selectElement.options;
                    for (var i = 0; i < options.length; i++) {{
                        if (options[i].value.includes("{target_month}")) {{
                            selectElement.selectedIndex = i;
                            selectElement.dispatchEvent(new Event("change", {{ bubbles: true }}));
                            return true; // 修复：明确返回值
                        }}
                    }}
                }}
                return false; // 修复：明确返回值
            }})();
            """

            month_result = await page.evaluate(js_code_2)
            if month_result:
                logger.info(f"✅ JavaScript选择月份成功")
            else:
                logger.warning(f"⚠️ JavaScript选择月份失败")


        await page.wait_for_timeout(2000)
        return True

    except Exception as e:
        logger.error(f"设置债权年月失败: {e}")
        return False

async def convert_debt_csv_to_xlsx(csv_path):
    """将债权CSV转换为XLSX格式"""
    try:
        import pandas as pd
        
        # 尝试多种方法读取债权CSV文件
        df = None
        
        # 方法1: 尝试不同编码
        encodings = ['shift_jis', 'utf-8', 'cp932', 'euc-jp', 'iso-2022-jp']
        for encoding in encodings:
            try:
                df = pd.read_csv(csv_path, encoding=encoding, on_bad_lines='skip')
                logger.info(f"使用编码 {encoding} 读取债权CSV文件成功，共{len(df)}行数据")
                break
            except Exception as e:
                logger.debug(f"编码 {encoding} 读取失败: {e}")
                continue
        
        # 方法2: 如果编码失败，尝试错误处理
        if df is None:
            for encoding in ['shift_jis', 'cp932', 'utf-8']:
                try:
                    df = pd.read_csv(csv_path, encoding=encoding, errors='ignore', on_bad_lines='skip')
                    logger.info(f"使用编码 {encoding} (忽略错误) 读取债权CSV文件成功，共{len(df)}行数据")
                    break
                except Exception as e:
                    logger.debug(f"编码 {encoding} (忽略错误) 读取失败: {e}")
                    continue
        
        # 方法3: 如果还是失败，尝试二进制读取并清理
        if df is None:
            try:
                # 读取原始字节并清理
                with open(csv_path, 'rb') as f:
                    raw_data = f.read()
                
                # 尝试检测编码
                import chardet
                detected = chardet.detect(raw_data)
                detected_encoding = detected.get('encoding', 'shift_jis')
                logger.info(f"检测到债权文件编码: {detected_encoding}")
                
                # 使用检测到的编码读取
                df = pd.read_csv(csv_path, encoding=detected_encoding, errors='ignore', on_bad_lines='skip')
                logger.info(f"使用检测编码 {detected_encoding} 读取债权CSV文件成功，共{len(df)}行数据")
                
            except Exception as e:
                logger.error(f"债权文件二进制读取也失败: {e}")
                # 最后尝试：直接复制文件作为XLSX
                try:
                    import shutil
                    xlsx_path = csv_path.replace('.csv', '.xlsx')
                    shutil.copy2(csv_path, xlsx_path)
                    logger.warning(f"无法转换债权CSV，直接复制文件: {xlsx_path}")
                    return xlsx_path
                except Exception as e2:
                    logger.error(f"债权文件复制也失败: {e2}")
                    return csv_path
        
        if df is None:
            logger.error("所有方法都无法读取债权CSV文件")
            return csv_path
        
        # 生成XLSX文件名
        xlsx_path = csv_path.replace('.csv', '.xlsx')
        
        # 保存为XLSX格式
        with pd.ExcelWriter(xlsx_path, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, header=True)
        logger.info(f"✅ 债权CSV转XLSX格式成功: {xlsx_path}")
        
        return xlsx_path
        
    except Exception as e:
        logger.error(f"债权CSV转XLSX转换失败: {e}")
        return csv_path

async def update_debt_sheets_with_client(xlsx_path, params, data_start_row=1):
    """使用sheets_client更新债权数据到1690表"""
    try:
        from core.gsuite.sheets_client import SheetsClient
        import pandas as pd
        import os
        
        # 读取Excel文件，跳过前面的行，从指定行开始读取数据
        df = pd.read_excel(xlsx_path, engine='openpyxl', skiprows=data_start_row-1)
        logger.info(f"从第{data_start_row}行开始读取债权数据，共{len(df)}行")
        
        # 创建临时文件，只包含需要的数据
        temp_xlsx_path = xlsx_path.replace('.xlsx', f'_debt_data_only.xlsx')
        df.to_excel(temp_xlsx_path, index=False, engine='openpyxl')
        
        # 从params获取数据处理规则
        data_processing_rules = params.get('data_processing_rules', [])
        if not data_processing_rules:
            # 默认的1690表规则
            data_processing_rules = [{
                'target_sheet_id': "1q7Z-qQKwDr_nSG73M4NvPicne6sFsr1vAYRvAtJLEQk",
                'header_row': 1,  # 临时文件的表头在第1行
                'paste_start_row': 4  # 目标表数据从第4行开始
            }]
        else:
            # 更新规则，因为现在使用的是处理过的临时文件
            for rule in data_processing_rules:
                rule['header_row'] = 1  # 临时文件的表头在第1行
        
        # 使用SheetsClient处理数据
        sheets_client = SheetsClient()
        sheets_client.process_and_write_data(temp_xlsx_path, data_processing_rules)
        
        # 清理临时文件
        try:
            os.remove(temp_xlsx_path)
            logger.debug(f"清理债权临时文件: {temp_xlsx_path}")
        except Exception as e:
            logger.warning(f"债权临时文件清理失败: {e}")
        
        logger.info(f"✅ 使用SheetsClient更新债权数据到1690表成功 (数据从第{data_start_row}行开始)")
        
    except Exception as e:
        logger.error(f"债权数据SheetsClient更新失败: {e}", exc_info=True)

async def update_google_sheets_with_data(df, step_config, xlsx_filename):
    """更新Google Sheets数据 - 保留原函数以兼容"""
    await update_google_sheets_with_processed_data(xlsx_filename, step_config)
