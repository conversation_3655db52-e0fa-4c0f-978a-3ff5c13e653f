
import os
import asyncio
import re
from dotenv import load_dotenv
from logger_config import logger
from core.browser.browser_manager import browser_manager
from core.gsuite.sheets_client import SheetsClient
from core.selector_executor import SelectorExecutor
from core.selectors_config import selectors_manager

# .envファイルから環境変数を読み込む
load_dotenv()


def clean_user_name(raw_name: str) -> str:
    """
    清理用户名，移除不需要的后缀和前缀

    Args:
        raw_name: 原始用户名文本

    Returns:
        清理后的用户名
    """
    if not raw_name:
        return "不明"

    # 移除常见的后缀
    suffixes_to_remove = [
        "様の基本情報へ遷移する",
        "の基本情報へ遷移する",
        "様の基本情報",
        "の基本情報",
        "様",
        "へ遷移する",
        "基本情報",
        "遷移する"
    ]

    cleaned_name = raw_name.strip()

    # 移除后缀
    for suffix in suffixes_to_remove:
        if cleaned_name.endswith(suffix):
            cleaned_name = cleaned_name[:-len(suffix)].strip()

    # 移除前缀（如果有的话）
    prefixes_to_remove = ["利用者:", "用户:", "名前:"]
    for prefix in prefixes_to_remove:
        if cleaned_name.startswith(prefix):
            cleaned_name = cleaned_name[len(prefix):].strip()

    # 如果清理后为空，返回原始名称的前几个字符
    if not cleaned_name:
        # 取原始名称的前10个字符作为备用
        cleaned_name = raw_name[:10].strip() if len(raw_name) > 10 else raw_name.strip()

    return cleaned_name if cleaned_name else "不明"


async def check_user_has_data(selector_executor: SelectorExecutor) -> bool:
    """快速检查用户页面是否有数据 - 架构师级别的效率优化"""
    try:
        # 等待页面基本加载
        await selector_executor.page.wait_for_timeout(1000)

        # 快速检查数据表格是否存在
        table_selectors = [
            "#tableData > tbody",
            "#tableData tbody",
            "table#tableData tbody",
            ".data-table tbody",
            "table tbody"
        ]

        for selector in table_selectors:
            try:
                # 检查表格是否存在且有内容
                table_element = selector_executor.page.locator(selector).first
                await table_element.wait_for(state="attached", timeout=2000)

                # 检查是否有数据行
                rows = await selector_executor.page.locator(f"{selector} tr").all()
                if len(rows) > 0:
                    logger.info(f"✅ 检测到数据表格: {selector}, {len(rows)} 行")
                    return True

            except Exception:
                continue

        # 备用检查：查找常见的"无数据"提示
        no_data_indicators = [
            "データがありません",
            "データなし",
            "記録がありません",
            "該当するデータがありません",
            "No data",
            "データが見つかりません"
        ]

        page_text = await selector_executor.page.inner_text("body")
        for indicator in no_data_indicators:
            if indicator in page_text:
                logger.info(f"🔍 检测到无数据提示: {indicator}")
                return False

        logger.info("⚠️ 无法确定数据状态，假设有数据")
        return True  # 保守策略：不确定时假设有数据

    except Exception as e:
        logger.warning(f"⚠️ 数据检查异常: {e}")
        return True  # 异常时假设有数据，避免遗漏


async def get_user_name_from_page(selector_executor: SelectorExecutor) -> str:
    """从用户页面获取用户名"""
    try:
        # 尝试多种选择器获取干净的用户名
        user_name_selectors = [
            ".txt-transition a",  # 原始选择器
            "h1",  # 可能在h1标签中
            ".user-name",  # 可能的用户名类
            ".name",  # 简单的name类
            "title"  # 可能在title中
        ]

        for selector in user_name_selectors:
            try:
                user_name_element = selector_executor.page.locator(selector).first
                raw_user_name = await user_name_element.inner_text()

                # 清理用户名，移除不需要的后缀
                user_name = clean_user_name(raw_user_name)

                if user_name and user_name != "不明":
                    return user_name
            except Exception:
                continue

        return "不明"

    except Exception as e:
        logger.warning(f"用户名获取失败: {e}")
        return "不明"


async def create_placeholder_row_for_user(selector_executor: SelectorExecutor, user_name: str) -> list:
    """为无数据的用户创建占位行"""
    try:
        logger.info(f"📝 为用户 {user_name} 创建占位行（该用户无数据）")

        # 创建占位行数据，保持与正常数据相同的列数
        placeholder_row = [
            "",  # 日期
            user_name,  # 用户名
            "データなし",  # 服务类型
            "",  # 内外
            "",  # 时间
            "0",  # 时长
            "",  # 备注1
            "",  # 备注2
            "",  # 备注3
            "",  # 备注4
            "",  # 备注5
            "",  # 备注6
            "",  # 备注7
            "",  # 备注8
            "",  # 备注9
            "",  # 备注10
            "",  # 备注11
            ""   # 备注12
        ]

        logger.info(f"✅ 占位行创建完成: {user_name} - データなし")
        return [placeholder_row]

    except Exception as e:
        logger.error(f"❌ 占位行创建失败: {e}")
        # 即使创建占位行失败，也返回基本信息
        return [["", user_name, "データなし", "", "", "0", "", "", "", "", "", "", "", "", "", "", "", ""]]


def run(config: dict):
    """カイポケ実績レポートワークフローを実行します。"""
    asyncio.run(main_async(config))

async def main_async(config: dict):
    """メインの非同期実行関数 - 完全基于RPA平台代码重构，集成MCP机制"""
    logger.info("🚀 カイポケ実績レポートワークフローを開始します（RPA平台代码+MCP強化版）。")
    
    common_config = config.get('config', {})
    tasks = config.get('tasks', [])
    
    # 共通設定を取得
    login_url = common_config.get('login_url')
    spreadsheet_id = common_config.get('spreadsheet_id')
    corp_id = os.getenv(common_config.get('corporation_id_env'))
    login_id = os.getenv(common_config.get('login_id_env'))
    password = os.getenv(common_config.get('password_env'))

    if not all([login_url, spreadsheet_id, corp_id, login_id, password]):
        logger.error("必要な共通設定（URL、スプレッドシートID、認証情報）が不足しています。")
        return

    try:
        sheets_client = SheetsClient()
        sheets_client.spreadsheet_id = spreadsheet_id
        logger.info("Google Sheets クライアントが正常に初期化されました。")
    except Exception as e:
        logger.error(f"Google Sheets クライアントの初期化に失敗しました: {e}")
        return

    # 一次性ブラウザ起動とログイン
    await browser_manager.start_browser(headless=False)
    page = await browser_manager.get_page()
    selector_executor = SelectorExecutor(page)

    # 🆕 MCPバックアップツールを初期化
    await selector_executor.initialize_mcp_fallback()

    try:
        # 使用本地的kaipoke登录函数
        login_success = await kaipoke_login_with_enhanced_selectors(
            page, login_url, corp_id, login_id, password, selector_executor
        )

        if not login_success:
            logger.error("❌ カイポケへのログインに失敗しました。ワークフローを中断します。")
            return

        logger.info("✅ カイポケにログインしました。全据点の処理を開始します。")

        # 各タスクを順番に実行（ログイン済みのブラウザを使用）
        for task_config in tasks:
            await execute_facility_with_logged_browser(task_config, common_config, sheets_client, selector_executor)

        logger.info("✅ カイポケ実績レポートワークフローが完了しました（MCP強化版）。")

    finally:
        await browser_manager.close_browser()
        logger.info("ブラウザを終了しました。")

# 已移除重复的execute_single_facility_task函数，避免レセプト重复点击
# 现在只使用execute_facility_with_logged_browser函数进行据点处理

async def extract_user_data(selector_executor: SelectorExecutor) -> list:
    """利用者詳細ページからデータを抽出する - #tableData > tbody の全情報を抽出"""
    logger.info("📊 利用者データを抽出開始 - #tableData > tbody の全情報")

    try:
        # 等待页面加载
        await selector_executor.page.wait_for_timeout(2000)

        # 获取用户名 - 修复提取逻辑
        user_name = ""
        try:
            # 尝试多种选择器获取干净的用户名
            user_name_selectors = [
                ".txt-transition a",  # 原始选择器
                "h1",  # 可能在h1标签中
                ".user-name",  # 可能的用户名类
                ".name",  # 简单的name类
                "title"  # 可能在title中
            ]

            for selector in user_name_selectors:
                try:
                    user_name_element = selector_executor.page.locator(selector).first
                    raw_user_name = await user_name_element.inner_text()

                    # 清理用户名，移除不需要的后缀
                    user_name = clean_user_name(raw_user_name)

                    if user_name and user_name != "不明":
                        logger.info(f"👤 利用者名: {user_name} (原始: {raw_user_name})")
                        break
                except Exception:
                    continue

            if not user_name:
                user_name = "不明"
                logger.warning("所有用户名选择器都失败，使用默认值")

        except Exception as e:
            logger.warning(f"利用者名抽出エラー: {e}")
            user_name = "不明"

        # 抽取 #tableData > tbody 的所有数据
        logger.info("📋 #tableData > tbody の全データを抽出")
        extracted_data = []

        try:
            # 使用智能选择器等待表格加载
            table_found = False
            table_selectors = [
                "#tableData > tbody",
                "#tableData tbody",
                "table#tableData tbody",
                ".data-table tbody",
                "table tbody"
            ]

            for selector in table_selectors:
                try:
                    await selector_executor.page.wait_for_selector(selector, timeout=5000)
                    rows = await selector_executor.page.locator(f"{selector} tr").all()
                    if len(rows) > 0:
                        logger.info(f"📊 使用选择器 {selector} 找到 {len(rows)} 行数据")
                        table_found = True
                        break
                except Exception:
                    continue

            if not table_found:
                # 🆕 优雅处理无数据情况：用户页面可能没有数据表格
                logger.info("ℹ️ 该用户页面无数据表格，创建占位行")
                return await create_placeholder_row_for_user(selector_executor, user_name)

            for row_index, row in enumerate(rows):
                try:
                    # 获取该行的所有单元格
                    cells = await row.locator("td").all()
                    row_data = [user_name]  # 第一列是用户名

                    # 抽取每个单元格的文本
                    for cell in cells:
                        try:
                            cell_text = await cell.inner_text()
                            row_data.append(cell_text.strip())
                        except Exception as e:
                            logger.warning(f"单元格文本抽取失败: {e}")
                            row_data.append("")

                    extracted_data.append(row_data)
                    logger.debug(f"行 {row_index + 1}: {len(row_data)} 列数据")

                except Exception as e:
                    logger.warning(f"行 {row_index + 1} 处理失败: {e}")
                    continue


            if not extracted_data:
                logger.warning("⚠️ 表格数据为空，返回默认行")
                extracted_data = [[user_name, "データなし"]]

            logger.info(f"✅ データ抽出完了: {len(extracted_data)}行のデータを抽出")
            return extracted_data

        except Exception as e:
            logger.error(f"❌ 表格数据抽取失败: {e}")
            # 如果表格抽取失败，尝试抽取页面的其他信息
            try:
                logger.info("🔄 尝试抽取页面的其他可见信息...")
                page_text = await selector_executor.page.locator("body").inner_text()
                # 简单处理，返回用户名和页面摘要
                return [[user_name, "页面数据抽取失败", page_text[:100] + "..." if len(page_text) > 100 else page_text]]
            except Exception as e2:
                logger.error(f"❌ 备用数据抽取也失败: {e2}")
                return [[user_name, "数据抽取失败"]]

    except Exception as e:
        logger.warning(f"⚠️ データ抽出中にエラーが発生: {e}")
        # 🆕 エラー時も占位行を返す（用户名を保持）
        user_name = user_name if 'user_name' in locals() else "不明"
        logger.info(f"📝 エラー時の占位行作成: {user_name}")
        return await create_placeholder_row_for_user(selector_executor, user_name)

async def finalize_sheet(sheets_client: SheetsClient, task_config: dict, target_month_str: str):
    """シートの最終処理を行う"""
    logger.info("シートの最終処理を実行します。")
    year = target_month_str.split('/')[0]
    month = target_month_str.split('/')[1]

    sheets_client.append_values(task_config.get('year_paste_cell'), [[year]])
    
    # 月を必要な行数分貼り付け
    row_count = sheets_client.get_row_count(task_config.get('target_sheet_name'))
    if row_count > 0:
        month_values = [[month]] * (row_count -1) # ヘッダー分を引くなど調整が必要
        sheets_client.update_range(task_config.get('month_paste_range'), month_values)

    sheets_client.clear_values(task_config.get('status_cell'))
    sheets_client.append_values(task_config.get('status_cell'), [['完了']])
    logger.info("ステータスを「完了」に更新しました。")

# ========================================
# 登录和Agent fallback函数
# ========================================

async def kaipoke_login_with_enhanced_selectors(page, login_url, corporation_id, member_login_id, password, selector_executor):
    """カイポケにログインします（完全基于RPA平台代码的精确选择器，已删除doLogin方法）。"""
    logger.info("カイポケにログインします（完全基于RPA平台代码的精确选择器）...")

    try:
        # ログインページに移動
        await page.goto(login_url, wait_until='networkidle', timeout=60000)
        logger.info(f"ログインページにアクセスしました: {login_url}")

        # ページの内容を確認 - 与RPA代码一致的2秒等待
        await page.wait_for_timeout(2000)
        logger.info("ページ読み込み完了を2秒待機")

        # 基于RPA平台代码的选择器执行器を使用してログイン（完全删除doLogin方法）
        login_success = await selector_executor.execute_kaipoke_login(
            corporation_id, member_login_id, password
        )

        if login_success:
            logger.info("✅ カイポケへのログインが完了しました（RPA平台代码方式）。")
            return True
        else:
            logger.warning("⚠️ RPA选择器でのログインに失敗、Agentフォールバックを実行")
            # Agent fallback
            await call_agent_for_kaipoke_login(page, corporation_id, member_login_id, password)
            return True

    except Exception as e:
        logger.error(f"カイポケログイン中にエラーが発生しました: {e}", exc_info=True)
        # デバッグ用にスクリーンショットを保存
        try:
            screenshot_path = '/tmp/kaipoke_performance_login_error.png'
            await page.screenshot(path=screenshot_path)
            logger.error(f"エラー発生時のスクリーンショットを {screenshot_path} に保存しました。")
        except:
            pass

        # Agent fallback
        logger.info("🤖 エラーのためAgentフォールバックを実行")
        await call_agent_for_kaipoke_login(page, corporation_id, member_login_id, password)
        return True

async def call_agent_for_kaipoke_login(page, corporation_id, member_login_id, password):
    """RPA平台代码选择器失効時にAgentを呼び出してカイポケログイン処理を行う"""
    logger.info("RPA平台代码选择器が失効したため、Agentでカイポケログイン処理を実行します。")
    # 这里可以添加Agent调用逻辑
    pass

async def call_agent_for_kaipoke_performance_report(task_config, common_config):
    """RPA平台代码选择器失効時にAgentを呼び出してカイポケ実績レポート処理を行う"""
    logger.info("RPA平台代码选择器が失効したため、Agentでカイポケ実績レポート処理を実行します。")
    # 这里可以添加Agent调用逻辑
    pass

async def execute_facility_with_logged_browser(task_config: dict, common_config: dict, sheets_client: SheetsClient, selector_executor: SelectorExecutor):
    """ログイン済みブラウザを使用して単一の拠点タスクを実行します。"""
    task_id = task_config.get('task_id')
    facility_name = task_config.get('facility_name')
    logger.info(f"📋 タスク実行開始: {task_id} ({facility_name})")

    # 🆕 将common_config中的spreadsheet_id传递给task_config
    task_config['spreadsheet_id'] = common_config.get('spreadsheet_id')

    # シート関連の設定
    status_cell = task_config.get('status_cell')
    target_month_cell = task_config.get('target_month_cell')
    clear_range = task_config.get('clear_range')

    try:
        # 1. Google Sheetの準備
        logger.info(f"Google Sheetを準備します。対象シート: {task_config.get('target_sheet_name')}")
        sheets_client.clear_values(status_cell)
        sheets_client.append_values(status_cell, [['処理中']])
        target_month_str = sheets_client.get_cell_value(target_month_cell)
        if not target_month_str:
            raise ValueError(f"{target_month_cell}から年月を取得できませんでした。")
        logger.info(f"対象年月: {target_month_str}")

        # YYYYMM形式に変換
        target_ym = re.sub(r'[^0-9]', '', target_month_str)[:6]
        sheets_client.clear_values(clear_range)

        # 2. 据点処理を実行（ログイン済みブラウザを使用）
        page = selector_executor.page

        # 3. 目的のページまで移動 - 完全基于RPA代码的导航流程
        # 🆕 检查当前页面状态，避免重复点击レセプト
        current_url = page.url
        logger.debug(f"📄 当前页面URL: {current_url}")

        # 🆕 增强页面状态检测
        if "COM020101.do" in current_url:
            logger.info("✅ 已在据点选择页面，跳过レセプト菜单点击")
        elif "MEM087" in current_url or "plan_actual" in current_url:
            # 如果在用户详情页面或实绩管理页面，需要先返回据点选择页面
            logger.info("🔄 检测到在用户详情页面，先返回据点选择页面...")
            try:
                await page.goto("https://r.kaipoke.biz/kaipokebiz/common/COM020101.do?fromBP=true", wait_until='networkidle', timeout=30000)
                await page.wait_for_timeout(2000)
                logger.info("✅ 成功返回据点选择页面")
            except Exception as e:
                logger.warning(f"⚠️ 返回据点选择页面失败: {e}")
                # 继续尝试正常流程
        else:
            # 3.1 点击レセプト菜单 - 增强调试和备用选择器
            logger.info("📋 レセプトメニューをクリック")

            # 🆕 添加页面结构调试
            logger.info("🔍 调试主页面结构...")
            try:
                page_title = await page.title()
                logger.info(f"📄 页面标题: {page_title}")

                # 检查主要菜单容器
                main_containers = ['.mainCtg', '.main-menu', '.menu-container', 'nav', '.navigation']
                for container in main_containers:
                    exists = await page.locator(container).count() > 0
                    if exists:
                        try:
                            container_text = await page.locator(container).inner_text()
                            logger.info(f"📊 找到容器 {container}: 文本='{container_text[:100]}...'")
                        except:
                            logger.info(f"📊 找到容器 {container}: 无法获取文本")

                # 检查所有可能的レセプト相关链接
                receipt_selectors = [
                    '.mainCtg li:nth-of-type(1) a',
                    '.mainCtg li:first-child a',
                    'a[href*="receipt"]',
                    'a[href*="レセプト"]',
                    'a:has-text("レセプト")',
                    '.menu a:first-child',
                    'li:first-child a'
                ]

                for selector in receipt_selectors:
                    try:
                        count = await page.locator(selector).count()
                        if count > 0:
                            text = await page.locator(selector).first.inner_text()
                            logger.info(f"📊 找到可能的レセプト选择器 {selector}: 数量={count}, 文本='{text}'")
                    except:
                        logger.debug(f"检查选择器失败: {selector}")

            except Exception as debug_error:
                logger.warning(f"⚠️ 页面结构调试失败: {debug_error}")

            receipt_clicked = False
            for attempt in range(3):  # 最多重试3次
                try:
                    # 🆕 使用多个备用选择器
                    backup_selectors = [
                        '.mainCtg li:nth-of-type(1) a',
                        '.mainCtg li:first-child a',
                        'a[href*="receipt"]',
                        'a:has-text("レセプト")',
                        '.menu a:first-child',
                        'li:first-child a'
                    ]

                    selector_found = False
                    for backup_selector in backup_selectors:
                        try:
                            await page.wait_for_selector(backup_selector, timeout=2000)
                            logger.info(f"✅ 找到レセプト选择器: {backup_selector}")

                            # 尝试点击
                            await page.click(backup_selector)
                            receipt_clicked = True
                            logger.info(f"✅ レセプトメニューをクリック成功（試行 {attempt + 1}，选择器: {backup_selector}）")
                            selector_found = True
                            break
                        except:
                            continue

                    if selector_found:
                        break
                    else:
                        logger.warning(f"⚠️ レセプトメニュー試行 {attempt + 1}: 所有选择器都失败")

                except Exception as e:
                    logger.warning(f"⚠️ レセプトメニュー試行 {attempt + 1} でエラー: {e}")

                if attempt < 2:  # 最後の試行でなければ待機
                    await page.wait_for_timeout(3000)  # 增加等待时间

            if not receipt_clicked:
                logger.error("❌ レセプトメニューのクリックに失敗（全試行終了）")

                # 🆕 最后尝试：直接导航到据点选择页面
                logger.info("🔄 尝试直接导航到据点选择页面...")
                try:
                    await page.goto("https://r.kaipoke.biz/kaipokebiz/common/COM020101.do", wait_until='networkidle', timeout=30000)
                    await page.wait_for_timeout(2000)
                    current_url = page.url
                    if "COM020101.do" in current_url:
                        logger.info("✅ 直接导航到据点选择页面成功")
                        receipt_clicked = True
                    else:
                        logger.error("❌ 直接导航也失败")
                        return
                except Exception as nav_error:
                    logger.error(f"❌ 直接导航失败: {nav_error}")
                    return

            await page.wait_for_timeout(1000)  # RPA代码中的等待时间

        # 3.2 据点选择 - 使用element_text与其他工作流一致的方式
        element_text = task_config.get('element_text')
        service_center_name = task_config.get('service_center_name')
        logger.info(f"🏢 {service_center_name}据点を選択: {element_text}")
        try:
            # 首先等待元素出现
            await page.wait_for_selector(f'//a[contains(text(), "{element_text}")]', timeout=10000)
            logger.info(f"✅ 找到据点元素: {element_text}")

            # 点击据点
            await page.click(f'//a[contains(text(), "{element_text}")]', timeout=10000)
            await page.wait_for_timeout(3000)  # 等待页面加载
            logger.info(f"✅ {service_center_name}のページに移動しました")
        except Exception as e:
            logger.error(f"❌ 据点选择失败: {element_text}, {e}")
            return

        # 3.3 予定実績管理メニューにマウスオーバー（RPA代码中的hover操作）
        logger.info("📊 予定実績管理メニューにマウスオーバー")

        # 🆕 添加页面结构调试 - 检查为什么鹿儿岛失败而福冈成功
        logger.info(f"🔍 调试据点页面结构 - {service_center_name}")
        try:
            # 检查页面URL和基本信息
            current_url = page.url
            page_title = await page.title()
            logger.info(f"📄 当前页面URL: {current_url}")
            logger.info(f"📄 页面标题: {page_title}")

            # 检查jsddm菜单结构
            jsddm_exists = await page.locator("#jsddm").count() > 0
            logger.info(f"📊 #jsddm 菜单存在: {jsddm_exists}")

            if jsddm_exists:
                # 检查jsddm的子元素数量
                jsddm_children = await page.locator("#jsddm > *").count()
                logger.info(f"📊 #jsddm 子元素数量: {jsddm_children}")

                # 检查每个子元素
                for i in range(1, min(jsddm_children + 1, 6)):  # 最多检查前5个
                    child_selector = f"#jsddm > :nth-child({i})"
                    child_exists = await page.locator(child_selector).count() > 0
                    if child_exists:
                        try:
                            child_text = await page.locator(child_selector).inner_text()
                            child_img_exists = await page.locator(f"{child_selector} img").count() > 0
                            logger.info(f"📊 {child_selector}: 存在={child_exists}, 文本='{child_text}', 有img={child_img_exists}")
                        except:
                            logger.info(f"📊 {child_selector}: 存在={child_exists}, 无法获取详细信息")

                # 特别检查第3个子元素
                third_child_exists = await page.locator("#jsddm > :nth-child(3)").count() > 0
                third_child_img_exists = await page.locator("#jsddm > :nth-child(3) img").count() > 0
                logger.info(f"📊 目标选择器检查:")
                logger.info(f"   #jsddm > :nth-child(3) 存在: {third_child_exists}")
                logger.info(f"   #jsddm > :nth-child(3) img 存在: {third_child_img_exists}")

                if third_child_exists:
                    try:
                        third_child_text = await page.locator("#jsddm > :nth-child(3)").inner_text()
                        logger.info(f"   第3个子元素文本: '{third_child_text}'")
                    except:
                        logger.info(f"   第3个子元素无法获取文本")

        except Exception as debug_error:
            logger.warning(f"⚠️ 页面结构调试失败: {debug_error}")

        # 尝试hover操作
        try:
            await page.hover("#jsddm > :nth-child(3) img", timeout=10000)
            logger.info("✅ 予定実績管理メニューにマウスオーバー完了")
            # 增加等待时间，确保菜单完全展开
            await page.wait_for_timeout(2000)  # 从1000ms增加到2000ms
        except Exception as e:
            logger.error(f"❌ 予定実績管理メニューのマウスオーバーに失敗: {e}")

            # 🆕 失败后尝试备用选择器
            logger.info("🔄 尝试备用hover选择器...")
            backup_selectors = [
                "#jsddm > :nth-child(2) img",  # 第2个子元素
                "#jsddm > :nth-child(4) img",  # 第4个子元素
                "#jsddm img[alt*='予定']",     # 包含"予定"的img
                "#jsddm img[alt*='実績']",     # 包含"実績"的img
                "#jsddm img[src*='schedule']", # src包含schedule的img
                "#jsddm img[src*='performance']", # src包含performance的img
                "#jsddm li img",               # 任何li下的img
            ]

            hover_success = False
            for backup_selector in backup_selectors:
                try:
                    backup_exists = await page.locator(backup_selector).count() > 0
                    logger.info(f"🔄 尝试备用选择器: {backup_selector}, 存在: {backup_exists}")
                    if backup_exists:
                        await page.hover(backup_selector, timeout=5000)
                        logger.info(f"✅ 备用hover成功: {backup_selector}")
                        hover_success = True
                        await page.wait_for_timeout(2000)
                        break
                except Exception as backup_error:
                    logger.debug(f"备用选择器失败 {backup_selector}: {backup_error}")
                    continue

            if not hover_success:
                logger.error("❌ 所有hover选择器都失败，跳过此据点")
                return

        # 3.4 予定実績一覧をクリック（メニューが表示された後）- 修复版本
        logger.info("📋 予定実績一覧をクリック")
        try:
            # 增强的菜单点击逻辑
            menu_selector = "#jsddm > :nth-child(3) li a"

            # 首先检查菜单是否存在
            menu_exists = await page.locator(menu_selector).count() > 0
            if not menu_exists:
                logger.error("❌ 菜单元素不存在")
                return

            # 等待菜单变为可见，增加超时时间
            logger.info("⏳ 等待菜单变为可见...")
            await page.wait_for_selector(menu_selector, state="visible", timeout=10000)  # 从5000ms增加到10000ms

            # 再次hover确保菜单保持展开状态
            await page.hover("#jsddm > :nth-child(3) img", timeout=5000)
            await page.wait_for_timeout(500)

            # 点击菜单
            await page.click(menu_selector, timeout=10000)
            logger.info("✅ 予定実績一覧のクリック完了")
            await page.wait_for_timeout(2000)  # RPA代码中的等待时间
        except Exception as e:
            logger.error(f"❌ 予定実績一覧のクリックに失敗: {e}")
            # 添加调试信息
            try:
                menu_count = await page.locator("#jsddm > :nth-child(3) li a").count()
                logger.error(f"调试信息: 菜单元素数量 = {menu_count}")
                if menu_count > 0:
                    is_visible = await page.locator("#jsddm > :nth-child(3) li a").is_visible()
                    logger.error(f"调试信息: 菜单是否可见 = {is_visible}")
            except:
                pass
            return

        # 4. 年月を選択し、条件を指定 - 完全基于RPA代码的流程
        # 4.1 年月选择
        logger.info(f"📅 年月を選択: {target_ym}")
        if not await selector_executor.smart_select_option("kaipoke", "performance_report", "year_month_select", value=target_ym):
            logger.error("❌ 年月選択に失敗")
            return
        await page.wait_for_timeout(3000)  # RPA代码中的等待时间

        # 4.2 搜索条件按钮
        logger.info("🔍 検索条件ボタンをクリック")
        if not await selector_executor.smart_click("kaipoke", "performance_report", "search_conditions_button"):
            logger.error("❌ 検索条件ボタンのクリックに失敗")
            return
        await page.wait_for_timeout(1000)  # RPA代码中的等待时间

        # 4.3 复选框选择 - 按照RPA代码的顺序
        checkboxes = [
            ("checkbox_plan_data_0", "計画データ0"),
            ("checkbox_achievement_data_0", "実績データ0"),
            ("checkbox_plan_data_1", "計画データ1"),
            ("checkbox_achievement_data_1", "実績データ1"),
            ("checkbox_plan_data_2", "計画データ2")
        ]

        for checkbox_key, checkbox_name in checkboxes:
            logger.info(f"☑️ {checkbox_name}をチェック")
            if not await selector_executor.smart_click("kaipoke", "performance_report", checkbox_key):
                logger.error(f"❌ {checkbox_name}のチェックに失敗")
                return
            await page.wait_for_timeout(1000)  # RPA代码中的等待时间

        # 4.4 搜索确认按钮 - RPA代码中点击两次
        logger.info("✅ 検索条件確定ボタンをクリック（1回目）")
        if not await selector_executor.smart_click("kaipoke", "performance_report", "confirm_search_conditions_button"):
            logger.error("❌ 検索条件確定ボタン（1回目）のクリックに失敗")
            return
        await page.wait_for_timeout(1000)  # RPA代码中的等待时间

        logger.info("✅ 検索条件確定ボタンをクリック（2回目）")
        if not await selector_executor.smart_click("kaipoke", "performance_report", "confirm_search_conditions_button"):
            logger.error("❌ 検索条件確定ボタン（2回目）のクリックに失敗")
            return
        await page.wait_for_timeout(3000)  # RPA代码中的等待时间

        # 🆕 修复：搜索条件确定后，直接在当前页面工作，不进行任何URL导航
        current_url = page.url
        logger.debug(f"📄 搜索条件确定后的URL: {current_url}")
        logger.info("📊 搜索条件确定完成，直接在当前页面获取合计数并点击第一个用户")

        # 5. データ抽出処理
        # 🆕 选择数据提取方法：顺序导航（新）或分页导航（旧）
        use_sequential_navigation = task_config.get('use_sequential_navigation', True)  # 默认使用新方法

        if use_sequential_navigation:
            logger.info("📄 使用顺序导航方法进行数据提取...")
            await process_user_data_extraction_sequential(selector_executor, sheets_client, task_config)
        else:
            logger.info("📄 使用传统分页方法进行数据提取...")
            await process_user_data_extraction(selector_executor, sheets_client, task_config)

        # 6. 最終処理
        await finalize_sheet(sheets_client, task_config, target_month_str)

        # 7. 🆕 据点任务完成后，返回据点选择页面（为下一个据点做准备）
        logger.info("🔄 据点任务完成，返回据点选择页面...")
        try:
            await page.goto("https://r.kaipoke.biz/kaipokebiz/common/COM020101.do?fromBP=true", wait_until='networkidle', timeout=30000)
            await page.wait_for_timeout(2000)
            current_url = page.url
            if "COM020101.do" in current_url:
                logger.info("✅ 成功返回据点选择页面，准备处理下一个据点")
            else:
                logger.warning(f"⚠️ 返回据点选择页面后URL异常: {current_url}")
        except Exception as e:
            logger.warning(f"⚠️ 返回据点选择页面失败: {e}")
            # 不影响当前据点的完成状态，只是可能影响下一个据点

        logger.info(f"✅ タスク完了: {task_id}")

    except Exception as e:
        logger.error(f"❌ タスク '{task_id}' の実行中にエラーが発生しました: {e}", exc_info=True)
        if status_cell:
            try:
                sheets_client.append_values(status_cell, [['エラー']])
            except Exception as sheet_e:
                logger.error(f"シートへのエラー書き込みに失敗しました: {sheet_e}")



        # Agent fallback
        logger.info("🤖 エラーのためAgentフォールバックを実行")
        await call_agent_for_kaipoke_performance_report(task_config, common_config)

async def process_user_data_extraction_sequential(selector_executor: SelectorExecutor, sheets_client: SheetsClient, task_config: dict):
    """
    用户数据顺序导航抽取处理 - 新的简化架构

    核心流程：
    1. 导航到第一个用户详情页
    2. 顺序遍历每个用户（通过详情页内的下一个用户链接）
    3. 对每个用户抽取详细信息
    4. 将数据批量写入Google Sheets
    """
    page = selector_executor.page
    logger.info("🚀 开始用户数据顺序导航抽取流程")

    # 确保在正确的用户列表页面并获取合计数（T1数据）
    try:
        # 检查当前页面状态
        current_url = page.url
        logger.info(f"📄 当前页面URL: {current_url}")

        # 🆕 修复：不进行URL导航，直接在当前页面处理
        logger.info("📊 搜索条件确定完成，直接在当前页面获取合计数和处理用户数据")

        # 等待页面完全加载
        try:
            await page.wait_for_load_state('networkidle', timeout=15000)
            await page.wait_for_timeout(3000)
            logger.debug("✅ 页面加载完成")
        except Exception as load_error:
            logger.warning(f"⚠️ 页面加载等待超时: {load_error}")

        # 🆕 修复：直接获取合计数，不检测页面结构
        logger.info("📊 直接获取合计数并进入第一个用户...")

        # 🆕 简化的合计数获取 - 直接从页面文本提取
        total_count_text = "合計数不明"
        try:
            page_text = await page.text_content("body")
            if page_text:
                import re
                # 查找包含数字和"件"的文本模式
                count_patterns = [
                    r'合計\s*(\d+)\s*件',
                    r'(\d+)\s*件',
                    r'total\s*(\d+)',
                    r'count\s*(\d+)'
                ]
                for pattern in count_patterns:
                    count_match = re.search(pattern, page_text, re.IGNORECASE)
                    if count_match:
                        total_count_text = f"合計{count_match.group(1)}件"
                        logger.info(f"📊 通过文本匹配获取到合计数: {total_count_text}")
                        break

        except Exception as count_error:
            logger.warning(f"⚠️ 合计数获取失败: {count_error}")

        logger.info(f"📊 最终合计数: {total_count_text}")

    except Exception as e:
        logger.error(f"❌ 页面状态检查或合计数获取失败: {e}")
        total_count_text = "合計数取得失敗"
        logger.warning("⚠️ 使用默认合计数，继续处理...")

    # 简化统计变量 - 只记录网站记录数和合计数
    total_processed_users = 0
    website_total_records = 0  # 网站显示的总记录数累计
    no_data_users = []  # 记录为0的用户名列表
    failed_users = []   # 获取失败的用户名列表

    # 批量数据缓存
    batch_data_cache = []
    batch_size = 20
    logger.info(f"📊 使用批量写入模式，批次大小: {batch_size}")

    # 获取目标月份字符串
    target_month_str = task_config.get('target_month', '2025-01')

    try:
        # 🆕 修复：搜索条件确定后，直接点击第一个用户，不判断URL
        logger.info("📄 搜索条件确定后，直接点击第一个用户...")

        # 等待页面加载完成
        try:
            await page.wait_for_load_state('networkidle', timeout=10000)
            await page.wait_for_timeout(3000)
            logger.debug("✅ 页面加载完成")
        except Exception as load_error:
            logger.warning(f"⚠️ 页面加载等待超时: {load_error}")

        # 直接尝试点击第一个用户
        first_user_success = False
        user_selectors = [
            ".color-neutral:nth-child(2) a",  # 主要选择器
            ".color-neutral a",               # 通用选择器
            "tr:nth-child(2) td:nth-child(1) a",  # 备用选择器
            "table tr td a"                   # 表格中的任何链接
        ]

        for selector in user_selectors:
            try:
                link_count = await page.locator(selector).count()
                if link_count > 0:
                    logger.info(f"📄 找到用户链接，使用选择器: {selector}")
                    await page.click(selector, timeout=10000)
                    await page.wait_for_timeout(3000)
                    first_user_success = True
                    logger.info("✅ 成功点击第一个用户")
                    break
            except Exception as click_error:
                logger.debug(f"选择器 {selector} 点击失败: {click_error}")
                continue

        if not first_user_success:
            logger.error("❌ 无法点击第一个用户，结束处理")
            return

        # 步骤2: 顺序处理每个用户
        user_count = 0
        while True:
            user_count += 1
            logger.info(f"👤 处理用户 {user_count}")

            try:
                # 验证当前页面状态
                page_validation = await validate_user_detail_page(selector_executor)

                if not page_validation['valid']:
                    logger.error(f"❌ 用户 {user_count} 页面状态验证失败")
                    break

                # 提取用户数据和网站记录数
                extracted_data, username, user_website_records = await extract_user_data_sequential_enhanced(selector_executor, target_month_str)

                # 累计网站记录数
                website_total_records += user_website_records

                # 检查是否为占位行（无数据用户）
                is_placeholder = len(extracted_data) == 1 and len(extracted_data[0]) > 2 and extracted_data[0][2] == "データなし"

                if is_placeholder:
                    no_data_users.append(username)
                    logger.info(f"📝 用户 {username}: 记录数=0")
                elif user_website_records == 0:
                    no_data_users.append(username)
                    logger.info(f"📝 用户 {username}: 记录数=0")
                else:
                    logger.info(f"✅ 用户 {username}: 记录数={user_website_records}, 抽取数据={len(extracted_data)}行")

                # 添加到批量缓存
                batch_data_cache.extend(extracted_data)
                total_processed_users += 1

                # 批量写入检查
                if len(batch_data_cache) >= batch_size:
                    logger.info(f"📊 批量写入 {len(batch_data_cache)} 行数据...")
                    await write_batch_to_sheets(sheets_client, batch_data_cache, task_config)
                    batch_data_cache = []

                # 检查是否有下一个用户
                if not page_validation['has_next_user']:
                    logger.info(f"✅ 已处理完所有用户，共 {user_count} 个用户")
                    break

                # 导航到下一个用户
                next_user_success = await navigate_to_next_user(selector_executor)

                if not next_user_success:
                    logger.warning(f"⚠️ 无法导航到下一个用户，可能已到最后一个用户")
                    break

            except Exception as e:
                # 记录失败的用户
                failed_username = f"用户{user_count}"
                try:
                    # 尝试获取用户名
                    failed_username = await get_user_name_from_page(selector_executor)
                except:
                    pass

                failed_users.append(failed_username)
                logger.error(f"❌ 用户 {failed_username}: 获取失败 - {e}")

                # 尝试导航到下一个用户继续处理
                try:
                    if await navigate_to_next_user(selector_executor):
                        continue
                    else:
                        break
                except:
                    break

        # 写入剩余的批量数据
        if batch_data_cache:
            logger.info(f"📊 写入剩余的 {len(batch_data_cache)} 行数据...")
            await write_batch_to_sheets(sheets_client, batch_data_cache, task_config)

        # 生成最终报告
        logger.info("📊 顺序导航抽取完成统计:")
        logger.info(f"   总处理用户数: {total_processed_users}")
        logger.info(f"   网站总记录数: {website_total_records}")
        logger.info(f"   网站合计数(T1): {total_count_text}")
        logger.info(f"   记录为0的用户数: {len(no_data_users)}")
        logger.info(f"   获取失败的用户数: {len(failed_users)}")

        if no_data_users:
            logger.info(f"   记录为0的用户: {', '.join(no_data_users)}")

        if failed_users:
            logger.info(f"   获取失败的用户: {', '.join(failed_users)}")

        # 写入T1和T2数据到指定位置
        await write_summary_data_sequential(sheets_client, task_config, total_count_text, website_total_records)

    except Exception as e:
        logger.error(f"❌ 顺序导航抽取过程中发生严重错误: {e}")
        # 写入已缓存的数据
        if batch_data_cache:
            logger.info("📊 写入已缓存的数据...")
            await write_batch_to_sheets(sheets_client, batch_data_cache, task_config)


async def process_user_data_extraction(selector_executor: SelectorExecutor, sheets_client: SheetsClient, task_config: dict):
    """
    ユーザーデータ抽出処理 - 専業架構師アプローチ

    核心流程：
    1. 筛选后的数据可能分布在多页
    2. 需要遍历每一页的所有用户
    3. 对每个用户抽取详细信息
    4. 将数据写入Google Sheets
    """
    page = selector_executor.page
    logger.info("🚀 開始用户数据抽取流程")

    # 🆕 在开始处理前获取合计数（T1数据）
    total_count_text = await get_total_count_from_pager(page)
    logger.info(f"📊 获取到合计数: {total_count_text}")

    current_page = 1
    total_processed_users = 0
    total_extracted_rows = 0  # 实际抽取的数据行数（用于验证）
    website_total_records = 0  # 🆕 网站总数据量（所有用户的数据记录数之和）
    no_data_users = []  # 🆕 记录无数据的用户名列表

    # 批量数据缓存 - 架构师级别的优化
    batch_data_cache = []
    batch_size = 20  # 每20个用户批量写入一次，避免API限制

    logger.info(f"📊 使用批量写入模式，批次大小: {batch_size}")

    # 核心循环：处理所有页面直到没有更多数据
    while True:
        logger.info(f"📄 処理中のページ: {current_page}")

        # 获取当前页面的用户总数
        total_users_on_page = await get_total_users_on_current_page(page)

        # 🆕 优化的错误处理逻辑
        if total_users_on_page == -1:
            logger.warning("⚠️ 检测到明确的错误页面，尝试恢复到正确的用户列表页面")

            # 🆕 错误恢复策略：尝试恢复到正确的分页状态
            recovery_success = await recover_from_error_page(page, current_page)

            if recovery_success:
                # 重新获取用户总数
                total_users_on_page = await get_total_users_on_current_page(page)
                if total_users_on_page >= 0:  # 🆕 接受0作为有效值
                    logger.info(f"✅ 错误恢复成功，当前页面有 {total_users_on_page} 个用户")
                else:
                    logger.warning("⚠️ 错误恢复后仍检测到错误，结束处理")
                    break
            else:
                logger.error("❌ 错误恢复失败，结束处理")
                break

        # 🆕 更宽松地处理无用户数据的情况
        if total_users_on_page == 0:
            logger.info("📄 当前页面没有用户数据，这可能是正常情况（数据为空或已处理完）")
            # 🆕 不立即结束，而是尝试检查是否有下一页
            logger.info("🔍 检查是否有下一页...")

            # 检查分页按钮
            try:
                next_page_exists = await page.locator('.next02 a').count() > 0
                if next_page_exists:
                    logger.info("📄 发现下一页按钮，继续分页处理")
                else:
                    logger.info("📄 没有下一页，处理完成")
                    break
            except:
                logger.info("📄 无法检测分页状态，结束处理")
                break

        logger.info(f"👥 当前页面共有 {total_users_on_page} 个用户需要处理")

        # 🆕 在处理用户前保存当前页面状态
        logger.debug("📄 保存当前页面状态...")
        await page_state_manager.save_current_page_state(page)

        # 🆕 验证页面状态保存是否成功
        if page_state_manager.user_list_base_url:
            logger.debug(f"✅ 页面状态保存成功，当前页码: {page_state_manager.current_page_number}")
        else:
            logger.warning("⚠️ 页面状态保存可能失败，将使用备用返回方法")

        # 🆕 使用新标签页方案处理当前页面的每个用户 - 彻底解决分页状态丢失问题
        logger.info(f"🚀 开始使用新标签页方案处理第{current_page}页的{total_users_on_page}个用户")

        for user_index in range(total_users_on_page):
            try:
                logger.info(f"👤 処理用户 {user_index + 1}/{total_users_on_page} (全体第{total_processed_users + 1}人) [新标签页方案]")

                # 🆕 在新标签页中抽取用户数据 - 主页面始终保持在用户列表页面
                extracted_data, username = await extract_user_data_from_page_new_tab(page, user_index, target_month_str)

                # 🆕 检查是否为占位行（无数据用户）
                is_placeholder = len(extracted_data) == 1 and len(extracted_data[0]) > 2 and extracted_data[0][2] == "データなし"

                if is_placeholder:
                    # 记录无数据用户
                    no_data_users.append(username)
                    logger.info(f"📝 记录无数据用户: {username}")
                    logger.debug(f"用户 {user_index + 1} 无数据（占位行），不计入数据统计")
                else:
                    # 🆕 累计实际抽取的数据行数（用于与网站总数据量对比）
                    total_extracted_rows += len(extracted_data)
                    logger.debug(f"累计抽取数据行数: {total_extracted_rows} 行")

                    # 注意：由于使用新标签页，网站记录数统计需要在用户详情页面获取
                    # 这里暂时跳过，或者可以在extract_user_data_from_page_new_tab中获取
                    logger.debug(f"用户 {user_index + 1} 数据抽取完成，共 {len(extracted_data)} 行")

                # 添加到批量缓存而不是立即写入 - 架构师优化
                batch_data_cache.extend(extracted_data)
                logger.debug(f"用户 {user_index + 1} 数据已缓存，当前缓存大小: {len(batch_data_cache)} 行")

                # 🆕 新标签页方案的核心优势：无需返回用户列表，主页面始终保持在用户列表页面
                logger.debug(f"✅ 用户 {user_index + 1} 処理完了，主页面保持在第{current_page}页用户列表")

                total_processed_users += 1
                logger.info(f"✅ 用户 {user_index + 1} 処理完了 (已缓存)")

                # 批量写入检查 - 避免API限制
                if len(batch_data_cache) >= batch_size or (user_index == total_users_on_page - 1):
                    logger.info(f"📝 执行批量写入: {len(batch_data_cache)} 行数据")
                    await batch_write_to_sheets(sheets_client, task_config, batch_data_cache)
                    batch_data_cache.clear()  # 清空缓存
                    logger.info("✅ 批量写入完成，缓存已清空")



            except Exception as e:
                logger.error(f"❌ 用户 {user_index + 1} 処理失败: {e}")
                # 尝试恢复到用户列表页面
                await safe_return_to_user_list(selector_executor)
                continue

        # 🆕 在分页前确保返回用户列表页面并验证页面状态
        logger.info("📄 当前页面处理完成，准备检查下一页...")

        # 🆕 增强的分页前页面状态验证
        page_ready_for_pagination = await ensure_ready_for_pagination(selector_executor, current_page)

        if not page_ready_for_pagination:
            logger.error("❌ 页面状态不适合分页，结束处理")
            break

        # 尝试翻到下一页
        has_next_page = await navigate_to_next_page(selector_executor)

        if not has_next_page:
            logger.info("没有更多页面，処理完了")
            break

        current_page += 1
        logger.info(f"📄 成功进入第 {current_page} 页")

    # 处理剩余的缓存数据 - 确保所有数据都被写入
    if batch_data_cache:
        logger.info(f"📝 写入剩余缓存数据: {len(batch_data_cache)} 行")
        await batch_write_to_sheets(sheets_client, task_config, batch_data_cache)
        batch_data_cache.clear()
        logger.info("✅ 剩余数据写入完成")

    logger.info(f"✅ 全ページ処理完了 - 総処理用户数: {total_processed_users}")
    logger.info(f"📊 批量写入优化: 减少了API调用次数，提高了性能")

    # 🆕 输出无数据用户列表
    if no_data_users:
        logger.info(f"📋 无数据用户列表 ({len(no_data_users)} 个用户):")
        for i, user_name in enumerate(no_data_users, 1):
            logger.info(f"   {i}. {user_name}")
    else:
        logger.info("✅ 所有用户都有数据")

    # 🆕 写入汇总数据到T1和T2位置
    logger.info("📝 写入汇总数据到T1和T2位置...")
    logger.info(f"   T1数据（页面显示合计数）: {total_count_text}")
    logger.info(f"   T2数据（网站总数据量）: {website_total_records} 条")
    logger.info(f"   实际抽取数据量: {total_extracted_rows} 行")
    logger.info(f"   有数据用户: {total_processed_users - len(no_data_users)} 个")
    logger.info(f"   无数据用户: {len(no_data_users)} 个")
    logger.info(f"   数据完整性: {total_extracted_rows}/{website_total_records} = {(total_extracted_rows/website_total_records*100) if website_total_records > 0 else 0:.1f}%")
    await write_summary_data_to_sheets(sheets_client, task_config, total_count_text, website_total_records)
    logger.info("✅ 汇总数据写入完成")


async def get_total_users_on_current_page(page) -> int:
    """获取当前页面的用户总数 - 简化版本，专注于实际功能"""
    try:
        current_url = page.url
        logger.info(f"📄 检查用户总数，当前URL: {current_url}")

        # 🔧 修复：确保在正确的页面
        if "MEM087001.do" not in current_url:
            logger.warning(f"⚠️ 不在用户列表页面: {current_url}")
            return -1

        # 🔧 修复：简化页面加载等待
        logger.debug("📄 等待页面基本加载...")
        try:
            await page.wait_for_load_state('domcontentloaded', timeout=5000)
            await page.wait_for_timeout(1000)  # 简短等待
        except:
            logger.debug("⏰ 页面加载等待超时，继续处理")

        # 🔧 修复：直接检查页面结构，不做复杂的错误判断
        logger.debug("🔍 检查页面基本结构...")

        # 等待表单元素
        try:
            await page.wait_for_selector("#form", timeout=5000)
            logger.debug("✅ #form 元素已加载")
        except:
            logger.info("📄 #form 元素不存在，可能是无数据页面")
            return 0

        # 🔧 修复：使用正确的选择器检查用户行
        # 直接检查.color-neutral行，这些是实际的用户行
        try:
            color_neutral_rows = await page.locator(".color-neutral").all()
            user_count = len(color_neutral_rows)

            logger.debug(f"📊 .color-neutral 用户行数: {user_count}")

            if user_count == 0:
                # 如果没有.color-neutral行，尝试传统方法
                logger.debug("📊 没有找到.color-neutral行，尝试传统表格检测...")
                user_table_selector = "#form > div.box-btn.mar-b-0.clearfix > table > tbody"

                # 检查表格是否存在
                table_exists = await page.locator(user_table_selector).count() > 0
                logger.debug(f"📊 用户表格存在: {table_exists}")

                if not table_exists:
                    logger.info("📄 用户表格不存在，可能无数据")
                    return 0

                # 获取用户行数
                user_rows = await page.locator(f"{user_table_selector} > tr").all()
                total_rows = len(user_rows)
                logger.debug(f"📊 表格总行数: {total_rows}")

                # 第一行通常是表头，实际用户数 = 总行数 - 1
                user_count = max(0, total_rows - 1)

            # 🔧 修复：验证是否真的有用户链接 - 使用正确的选择器
            if user_count > 0:
                try:
                    # 检查第一个用户链接是否存在
                    first_user_link_count = await page.locator(".color-neutral:nth-child(2) a").count()
                    if first_user_link_count == 0:
                        # 尝试更通用的选择器
                        first_user_link_count = await page.locator(".color-neutral a").count()

                    if first_user_link_count == 0:
                        logger.info("📄 .color-neutral行存在但无用户链接，可能是无数据提示")
                        user_count = 0
                    else:
                        logger.debug(f"✅ 找到 {first_user_link_count} 个用户链接")
                except Exception as e:
                    logger.debug(f"⚠️ 用户链接验证异常: {e}")
                    pass

            logger.info(f"📊 当前页面用户数: {user_count}")
            return user_count

        except Exception as e:
            logger.warning(f"⚠️ 获取用户行数失败: {e}")
            return 0

    except Exception as e:
        logger.warning(f"❌ 用户总数获取失败: {e}")
        return 0  # 异常时返回0，避免触发错误恢复


async def get_users_on_current_page(page) -> list:
    """获取当前页面的用户列表（保留兼容性）"""
    total_users = await get_total_users_on_current_page(page)
    return [{"index": i} for i in range(total_users)]


async def count_website_data_for_user(selector_executor) -> int:
    """统计当前用户页面上网站实际有多少条数据"""
    try:
        page = selector_executor.page

        # 尝试多种方式统计网站数据
        count_methods = [
            # 方法1：统计表格行数
            {
                "method": "表格行数统计",
                "selectors": ["#tableData tbody tr", "table tbody tr", ".data-table tbody tr"]
            },
            # 方法2：从分页信息获取
            {
                "method": "分页信息统计",
                "selectors": [".pager-btm", ".pagination-info", ".page-info"]
            },
            # 方法3：统计数据行
            {
                "method": "数据行统计",
                "selectors": ["tr[data-*]", ".data-row", ".record-row"]
            }
        ]

        for method in count_methods:
            try:
                for selector in method["selectors"]:
                    elements = page.locator(selector)
                    count = await elements.count()

                    if count > 0:
                        # 如果是表格行，需要排除表头
                        if "tbody tr" in selector:
                            # 检查是否有表头行需要排除
                            header_count = await page.locator("thead tr, tbody tr:first-child th").count()
                            actual_count = max(0, count - (1 if header_count > 0 else 0))
                        else:
                            actual_count = count

                        if actual_count > 0:
                            logger.debug(f"✅ {method['method']}成功: {selector} = {actual_count} 条数据")
                            return actual_count
            except Exception:
                continue

        logger.warning("⚠️ 无法统计网站数据，使用默认值0")
        return 0

    except Exception as e:
        logger.error(f"❌ 网站数据统计失败: {e}")
        return 0


async def get_total_count_from_pager(page) -> str:
    """从.pager-btm p元素获取合计数"""
    try:
        # 尝试多种选择器获取合计数
        pager_selectors = [
            ".pager-btm p",
            ".pager-bottom p",
            ".pagination-info p",
            ".page-info p",
            "p:contains('合計')",
            "p:contains('総計')",
            "p:contains('件')"
        ]

        for selector in pager_selectors:
            try:
                element = page.locator(selector).first
                text = await element.inner_text()
                if text and text.strip():
                    logger.info(f"✅ 合计数获取成功: {text.strip()} (选择器: {selector})")
                    return text.strip()
            except Exception:
                continue

        logger.warning("⚠️ 未能获取合计数，使用默认值")
        return "合計数取得失敗"

    except Exception as e:
        logger.error(f"❌ 合计数获取失败: {e}")
        return "合計数取得失敗"


async def get_website_total_records(page) -> int:
    """获取网站上的总数据记录数（T2数据）- 简化版本"""
    try:
        # 直接从已知的.pager-btm p元素获取总记录数
        try:
            element = await page.locator(".pager-btm p").first.wait_for(timeout=5000)
            text = await element.inner_text()
            if text and text.strip():
                import re
                # 匹配模式：合計：XXX件
                match = re.search(r'合計[：:]\s*(\d+)\s*件', text)
                if match:
                    total_records = int(match.group(1))
                    logger.info(f"✅ 网站总记录数获取成功: {total_records} 条 (从: {text.strip()})")
                    return total_records
        except Exception as e:
            logger.debug(f"从.pager-btm p获取总记录数失败: {e}")

        logger.warning("⚠️ 无法获取网站总记录数，返回0")
        return 0

    except Exception as e:
        logger.error(f"❌ 网站总记录数获取失败: {e}")
        return 0


async def click_user_by_index_new_tab(page, user_index: int):
    """在新标签页中打开用户详情 - 架构师级别的分页状态保持方案"""
    # 使用正确的用户选择器，注意tr从第2行开始（第1行是表头）
    row_index = user_index + 2  # 第2行是第一个用户，第3行是第二个用户，以此类推
    user_selector = f"#form > div.box-btn.mar-b-0.clearfix > table > tbody > tr:nth-child({row_index}) > td:nth-child(1) > a"

    try:
        logger.debug(f"📄 在新标签页中打开用户 {user_index + 1}")

        # 🆕 获取用户链接的href属性
        user_link = page.locator(user_selector)
        if await user_link.count() == 0:
            logger.error(f"❌ 用户链接不存在: {user_selector}")
            return None

        href = await user_link.get_attribute("href")
        if not href:
            logger.error(f"❌ 用户链接没有href属性")
            return None

        # 🆕 构造完整的URL
        if href.startswith('/'):
            base_url = page.url.split('/kaipokebiz/')[0]
            full_url = base_url + href
        elif href.startswith('http'):
            full_url = href
        else:
            current_base = '/'.join(page.url.split('/')[:-1])
            full_url = f"{current_base}/{href}"

        logger.debug(f"📄 用户详情URL: {full_url}")

        # 🆕 在新标签页中打开用户详情
        context = page.context
        new_page = await context.new_page()

        # 导航到用户详情页面
        await new_page.goto(full_url, wait_until='networkidle', timeout=30000)
        await new_page.wait_for_timeout(3000)

        logger.debug(f"✅ 成功在新标签页中打开用户 {user_index + 1}")
        return new_page

    except Exception as e:
        logger.error(f"❌ 在新标签页中打开用户失败: {e}")
        return None


async def debug_page_structure(page, context: str = ""):
    """调试页面结构，帮助诊断选择器问题"""
    try:
        logger.info(f"🔍 调试页面结构 - {context}")

        # 首先检查页面是否有效
        try:
            current_url = page.url
            logger.info(f"📄 当前URL: {current_url}")
        except Exception as url_error:
            logger.error(f"❌ 无法获取页面URL，页面可能已失效: {url_error}")
            return

        # 检查页面是否关闭
        try:
            is_closed = page.is_closed()
            logger.info(f"📄 页面是否关闭: {is_closed}")
            if is_closed:
                logger.error("❌ 页面已关闭，无法进行调试")
                return
        except:
            logger.warning("⚠️ 无法检查页面关闭状态")

        # 检查基本元素
        try:
            form_exists = await page.locator("#form").count() > 0
            logger.info(f"   #form 存在: {form_exists}")
        except Exception as e:
            logger.error(f"   ❌ 检查#form失败: {e}")
            return

        # 检查表格结构
        try:
            table_exists = await page.locator("#form table").count() > 0
            logger.info(f"   #form table 存在: {table_exists}")
        except Exception as e:
            logger.warning(f"   ⚠️ 检查表格失败: {e}")

        # 检查.color-neutral行
        try:
            color_neutral_count = await page.locator(".color-neutral").count()
            logger.info(f"   .color-neutral 行数: {color_neutral_count}")
        except Exception as e:
            logger.warning(f"   ⚠️ 检查.color-neutral失败: {e}")

        # 检查第一个用户链接
        try:
            first_user_link_exists = await page.locator(".color-neutral:nth-child(2) a").count() > 0
            logger.info(f"   .color-neutral:nth-child(2) a 存在: {first_user_link_exists}")
        except Exception as e:
            logger.warning(f"   ⚠️ 检查第一个用户链接失败: {e}")

        # 检查页面是否有错误信息
        error_indicators = [
            "text='エラー'",
            "text='Error'",
            "text='404'",
            "text='ページが見つかりません'"
        ]

        for indicator in error_indicators:
            try:
                error_exists = await page.locator(indicator).count() > 0
                if error_exists:
                    logger.warning(f"   ⚠️ 错误指示器发现: {indicator}")
            except:
                pass

    except Exception as e:
        logger.error(f"❌ 页面结构调试失败: {e}")


async def navigate_to_first_user(selector_executor: SelectorExecutor) -> bool:
    """导航到第一个用户详情页面"""
    try:
        page = selector_executor.page
        logger.info("📄 导航到第一个用户详情页面...")

        # 🆕 修复：直接在当前页面查找第一个用户，不进行任何导航
        current_url = page.url
        logger.debug(f"📄 当前URL: {current_url}")
        logger.info("📄 直接查找并点击第一个用户链接...")

        # 🆕 简化的页面验证 - 直接查找用户链接
        try:
            # 等待页面加载
            logger.debug("⏳ 等待页面加载...")
            try:
                await page.wait_for_load_state('networkidle', timeout=10000)
                await page.wait_for_timeout(2000)
                logger.debug("✅ 页面加载完成")
            except Exception as load_error:
                logger.warning(f"⚠️ 页面加载等待超时: {load_error}")

            # 步骤3: 等待关键元素加载 - 使用多重策略
            logger.debug("⏳ 等待页面关键元素...")
            key_element_found = False
            key_elements = [
                ("#form", "标准表单"),
                ("form", "通用表单"),
                ("table", "数据表格"),
                (".main-content", "主内容"),
                ("body", "页面主体")
            ]

            for selector, description in key_elements:
                try:
                    await page.wait_for_selector(selector, timeout=8000)
                    logger.debug(f"✅ {description}元素已加载: {selector}")
                    key_element_found = True
                    break
                except Exception as elem_error:
                    logger.debug(f"⚠️ {description}元素等待失败: {elem_error}")
                    continue

            if not key_element_found:
                logger.warning("⚠️ 未找到任何关键元素，但继续处理")

            # 步骤4: 🆕 灵活的用户链接查找 - 不依赖特定表格结构
            logger.debug("⏳ 查找用户链接...")
            user_links_found = False

            # 尝试多种用户链接选择器
            user_link_selectors = [
                ".color-neutral:nth-child(2) a",  # 主要选择器
                ".color-neutral a",               # 通用选择器
                "tr:nth-child(2) td:nth-child(1) a",  # 备用选择器
                "table tr td a",                  # 表格中的链接
                "a[href*='MEM087101']",          # 包含用户详情页面的链接
                "a"                              # 最后备用：任何链接
            ]

            for selector in user_link_selectors:
                try:
                    link_count = await page.locator(selector).count()
                    if link_count > 0:
                        logger.debug(f"✅ 找到 {link_count} 个用户链接: {selector}")
                        user_links_found = True
                        break
                except Exception as link_error:
                    logger.debug(f"⚠️ 用户链接选择器失败: {selector} - {link_error}")
                    continue

            if not user_links_found:
                logger.warning("⚠️ 未找到任何用户链接，但继续尝试点击")

            # 调试页面结构
            await debug_page_structure(page, "查找第一个用户链接前")

        except Exception as e:
            logger.error(f"❌ 等待用户列表页面加载失败: {e}")
            try:
                await debug_page_structure(page, "页面加载失败后")
            except:
                logger.error("❌ 连调试页面结构都失败了，页面可能已完全失效")
            return False

        # 获取用户总数
        total_users = await get_total_users_on_current_page(page)
        if total_users <= 0:
            logger.error("❌ 用户列表页面没有用户数据")
            return False

        logger.info(f"📊 用户列表页面共有 {total_users} 个用户")

        # 🆕 增强的第一个用户点击逻辑 - 多重验证和重试
        logger.info("📄 点击第一个用户...")

        # 点击前最终验证和调试
        await debug_page_structure(page, "点击第一个用户前最终检查")

        # 🆕 多重点击策略
        success = False
        click_attempts = [
            ("智能选择器", lambda: selector_executor.smart_click("kaipoke", "performance_report", "first_user_link")),
            ("直接选择器", lambda: page.click(".color-neutral:nth-child(2) a", timeout=10000)),
            ("JavaScript评估", lambda: page.evaluate("""
                () => {
                    const link = document.querySelector('.color-neutral:nth-child(2) a');
                    if (link) {
                        link.click();
                        return true;
                    }
                    return false;
                }
            """)),
            ("备用选择器", lambda: page.click("tr:nth-child(2) td:nth-child(1) a", timeout=10000))
        ]

        for attempt_name, click_func in click_attempts:
            try:
                logger.info(f"🔄 尝试{attempt_name}点击...")

                # 在每次尝试前确保元素存在
                if attempt_name == "智能选择器":
                    result = await click_func()
                    if result:
                        success = True
                        logger.info(f"✅ {attempt_name}点击成功")
                        break
                    else:
                        logger.warning(f"⚠️ {attempt_name}返回失败")
                        continue
                else:
                    # 对于直接点击，先验证元素存在
                    if attempt_name == "直接选择器":
                        await page.wait_for_selector(".color-neutral:nth-child(2) a", timeout=5000)
                        # 获取链接信息用于调试
                        link_element = page.locator(".color-neutral:nth-child(2) a").first
                        link_href = await link_element.get_attribute("href")
                        link_text = await link_element.text_content()
                        onclick_attr = await link_element.get_attribute("onclick")
                        logger.info(f"🔗 链接信息: href={link_href}, text={link_text}, onclick={onclick_attr}")
                    elif attempt_name == "备用选择器":
                        await page.wait_for_selector("tr:nth-child(2) td:nth-child(1) a", timeout=5000)

                    await click_func()
                    success = True
                    logger.info(f"✅ {attempt_name}点击成功")
                    break

            except Exception as e:
                logger.warning(f"⚠️ {attempt_name}点击失败: {e}")
                continue

        if not success:
            logger.error("❌ 所有点击策略都失败")
            await debug_page_structure(page, "所有点击策略失败后")
            return False

        # 🆕 增强的页面加载等待和验证
        logger.info("⏳ 等待用户详情页面加载...")

        # 等待页面导航完成
        try:
            await page.wait_for_load_state('networkidle', timeout=15000)
            await page.wait_for_timeout(5000)  # 增加等待时间确保页面完全加载
            logger.debug("✅ 页面导航和加载完成")
        except Exception as load_error:
            logger.warning(f"⚠️ 页面加载等待超时: {load_error}")

        # 点击后调试页面结构
        await debug_page_structure(page, "点击第一个用户后")

        # 🆕 增强的页面验证 - 多次尝试
        validation_success = False
        for validation_attempt in range(3):
            logger.debug(f"📄 页面验证尝试 {validation_attempt + 1}/3")

            # 等待关键元素出现
            try:
                await page.wait_for_selector("#form\\:userId", timeout=10000)
                logger.debug("✅ 找到用户ID字段")
            except Exception as userid_error:
                logger.warning(f"⚠️ 用户ID字段等待失败: {userid_error}")
                if validation_attempt < 2:
                    await page.wait_for_timeout(3000)
                    continue

            # 验证页面状态
            validation = await validate_user_detail_page(selector_executor)
            if validation['valid']:
                validation_success = True
                logger.info("✅ 成功导航到第一个用户详情页面")
                logger.info(f"📄 页面验证结果: {validation}")
                break
            else:
                logger.warning(f"⚠️ 页面验证失败 (尝试 {validation_attempt + 1}): {validation}")
                if validation_attempt < 2:
                    await page.wait_for_timeout(3000)

        if not validation_success:
            logger.error("❌ 导航后页面状态验证最终失败")
            await debug_page_structure(page, "页面验证最终失败后")
            return False

        return True

    except Exception as e:
        logger.error(f"❌ 导航到第一个用户详情页面失败: {e}")
        return False


async def navigate_to_next_user(selector_executor: SelectorExecutor) -> bool:
    """导航到下一个用户详情页面 - 增强版多重点击策略"""
    try:
        page = selector_executor.page
        logger.info("📄 导航到下一个用户...")

        # 步骤1: 验证当前在用户详情页面
        validation = await validate_user_detail_page(selector_executor)
        if not validation['valid']:
            logger.error("❌ 当前不在有效的用户详情页面")
            return False

        if not validation['has_next_user']:
            logger.info("📄 没有下一个用户，已到最后一个用户")
            return False

        # 步骤2: 增强的元素等待和状态检查
        logger.debug("🔍 检查下一个用户链接状态...")
        try:
            # 等待元素出现并确保可见
            await page.wait_for_selector(".table-linkuser td:nth-of-type(3) a", timeout=10000)

            # 获取链接信息用于调试和MCP备份
            next_link_element = page.locator(".table-linkuser td:nth-of-type(3) a").first
            link_text = await next_link_element.text_content()
            link_href = await next_link_element.get_attribute("href")
            onclick_attr = await next_link_element.get_attribute("onclick")

            logger.debug(f"🔗 下一个用户链接信息: text='{link_text}', href='{link_href}', onclick='{onclick_attr}'")

            # 检查元素是否可见和可交互
            is_visible = await next_link_element.is_visible()
            is_enabled = await next_link_element.is_enabled()
            logger.debug(f"📊 元素状态: visible={is_visible}, enabled={is_enabled}")

        except Exception as e:
            logger.warning(f"⚠️ 元素状态检查失败: {e}")
            link_text = "次へ"  # 默认文本用于MCP备份

        # 步骤3: 多重点击策略 - 参考第一个用户点击的成功模式
        success = False
        click_attempts = [
            ("智能选择器+MCP备份", lambda: selector_executor.smart_click("kaipoke", "performance_report", "next_user_button", target_text="次へ")),
            ("直接Playwright点击", lambda: page.click(".table-linkuser td:nth-of-type(3) a", timeout=15000)),
            ("JavaScript强制点击", lambda: page.evaluate("""
                () => {
                    const link = document.querySelector('.table-linkuser td:nth-of-type(3) a');
                    if (link) {
                        link.click();
                        return true;
                    }
                    return false;
                }
            """)),
            ("备用选择器点击", lambda: page.click(".table-linkuser td:nth-child(3) a", timeout=10000)),
            ("通用备用选择器", lambda: page.click(".user-navigation td:nth-of-type(3) a", timeout=10000))
        ]

        for attempt_name, click_func in click_attempts:
            try:
                logger.info(f"🔄 尝试{attempt_name}...")

                if attempt_name == "智能选择器+MCP备份":
                    # 智能选择器会自动尝试MCP备份
                    result = await click_func()
                    if result:
                        success = True
                        logger.info(f"✅ {attempt_name}成功")
                        break
                    else:
                        logger.warning(f"⚠️ {attempt_name}返回失败")
                        continue
                elif attempt_name == "JavaScript强制点击":
                    # JavaScript点击返回布尔值
                    result = await click_func()
                    if result:
                        success = True
                        logger.info(f"✅ {attempt_name}成功")
                        break
                    else:
                        logger.warning(f"⚠️ {attempt_name}未找到元素")
                        continue
                else:
                    # 直接点击方法
                    await click_func()
                    success = True
                    logger.info(f"✅ {attempt_name}成功")
                    break

            except Exception as e:
                logger.warning(f"⚠️ {attempt_name}失败: {e}")
                continue

        if not success:
            logger.error("❌ 所有点击策略都失败")
            return False

        # 步骤4: 增强的页面加载等待和验证
        logger.info("⏳ 等待下一个用户页面加载...")

        # 等待页面导航完成
        try:
            await page.wait_for_load_state('networkidle', timeout=15000)
            await page.wait_for_timeout(3000)  # 确保页面完全加载
            logger.debug("✅ 页面导航和加载完成")
        except Exception as load_error:
            logger.warning(f"⚠️ 页面加载等待超时: {load_error}")

        # 步骤5: 验证成功进入下一个用户详情页面
        new_validation = await validate_user_detail_page(selector_executor)
        if new_validation['valid']:
            logger.info("✅ 成功导航到下一个用户详情页面")
            return True
        else:
            logger.error("❌ 导航后页面状态验证失败")
            logger.debug(f"📄 验证结果: {new_validation}")
            return False

    except Exception as e:
        logger.error(f"❌ 导航到下一个用户失败: {e}")
        return False


async def validate_user_detail_page(selector_executor: SelectorExecutor) -> dict:
    """验证用户详情页面状态"""
    try:
        page = selector_executor.page
        result = {
            'valid': False,
            'has_next_user': False,
            'user_id': '',
            'has_data_table': False
        }

        # 检查用户ID字段（关键验证）
        user_id_exists = await page.locator("#form\\:userId").count() > 0
        if user_id_exists:
            try:
                user_id_value = await page.locator("#form\\:userId").get_attribute("value")
                result['user_id'] = user_id_value or ''
            except:
                pass

        # 检查数据表格
        data_table_exists = await page.locator("#tableData").count() > 0
        result['has_data_table'] = data_table_exists

        # 检查下一个用户链接
        next_user_exists = await page.locator(".table-linkuser td:nth-of-type(3) a").count() > 0
        result['has_next_user'] = next_user_exists

        # 页面有效性：必须有用户ID字段
        result['valid'] = user_id_exists

        logger.debug(f"📄 页面验证结果: valid={result['valid']}, has_next_user={result['has_next_user']}, user_id={result['user_id']}")

        return result

    except Exception as e:
        logger.error(f"❌ 页面状态验证异常: {e}")
        return {
            'valid': False,
            'has_next_user': False,
            'user_id': '',
            'has_data_table': False
        }


async def extract_user_data_sequential_enhanced(selector_executor: SelectorExecutor, target_month_str: str) -> tuple:
    """在顺序导航模式下提取用户数据和网站记录数"""
    try:
        # 获取用户名
        username = await get_user_name_from_page(selector_executor)
        logger.debug(f"👤 当前用户: {username}")

        # 获取网站显示的记录数
        website_records = await get_user_website_record_count(selector_executor)

        # 检查用户是否有数据
        has_data = await check_user_has_data(selector_executor)

        if not has_data or website_records == 0:
            logger.debug(f"📄 用户 {username} 无数据，创建占位行")
            placeholder_data = [[username, target_month_str, "データなし", "", "", "", ""]]
            return placeholder_data, username, website_records

        # 提取实际数据
        extracted_data = await extract_user_data(selector_executor)

        if not extracted_data:
            logger.warning(f"⚠️ 用户 {username} 数据抽取为空，创建占位行")
            placeholder_data = [[username, target_month_str, "データなし", "", "", "", ""]]
            return placeholder_data, username, website_records

        logger.debug(f"✅ 用户 {username} 数据抽取完成: {len(extracted_data)} 行")
        return extracted_data, username, website_records

    except Exception as e:
        logger.error(f"❌ 顺序导航数据抽取失败: {e}")
        error_username = "抽取失败"
        error_data = [[error_username, target_month_str, "抽取失败", "", "", "", ""]]
        return error_data, error_username, 0


async def get_user_website_record_count(selector_executor: SelectorExecutor) -> int:
    """获取用户详情页面显示的网站记录数"""
    try:
        page = selector_executor.page

        # 尝试多种可能的记录数显示位置
        record_count_selectors = [
            ".pager-btm p",  # 分页区域的记录数显示
            ".record-count",  # 可能的记录数类
            ".total-count",   # 可能的总数类
            "p:contains('件')",  # 包含"件"的段落
            "span:contains('件')"  # 包含"件"的span
        ]

        for selector in record_count_selectors:
            try:
                element = page.locator(selector)
                if await element.count() > 0:
                    text = await element.first.text_content()
                    if text and "件" in text:
                        # 提取数字
                        import re
                        numbers = re.findall(r'\d+', text)
                        if numbers:
                            count = int(numbers[0])
                            logger.debug(f"📊 从 {selector} 获取到记录数: {count}")
                            return count
            except:
                continue

        # 如果没有找到记录数显示，检查是否有数据表格
        table_rows = await page.locator("#tableData tbody tr").count()
        if table_rows > 0:
            logger.debug(f"📊 从表格行数推算记录数: {table_rows}")
            return table_rows

        logger.debug("📊 未找到记录数显示，返回0")
        return 0

    except Exception as e:
        logger.warning(f"⚠️ 获取网站记录数失败: {e}")
        return 0


async def write_summary_data_sequential(sheets_client: SheetsClient, task_config: dict, total_count_text: str, website_total_records: int):
    """写入T1和T2汇总数据"""
    try:
        spreadsheet_id = task_config.get('spreadsheet_id')
        if not spreadsheet_id:
            logger.warning("⚠️ 缺少spreadsheet_id，跳过汇总数据写入")
            return

        # 🆕 设置SheetsClient的spreadsheet_id
        sheets_client.spreadsheet_id = spreadsheet_id

        # 获取T1和T2位置配置
        total_count_cell = task_config.get('total_count_cell')  # T1位置
        user_count_cell = task_config.get('user_count_cell')    # T2位置

        if total_count_cell:
            # 写入T1数据（页面显示的合计数）
            success = sheets_client.update_cell(
                cell_address=total_count_cell,
                value=total_count_text
            )
            if success:
                logger.info(f"✅ T1数据写入成功: {total_count_cell} = {total_count_text}")
            else:
                logger.error(f"❌ T1数据写入失败: {total_count_cell}")

        if user_count_cell:
            # 写入T2数据（网站实际数据总量）
            success = sheets_client.update_cell(
                cell_address=user_count_cell,
                value=str(website_total_records)
            )
            if success:
                logger.info(f"✅ T2数据写入成功: {user_count_cell} = {website_total_records}")
            else:
                logger.error(f"❌ T2数据写入失败: {user_count_cell}")

        # 数据对比验证
        if total_count_text and website_total_records > 0:
            try:
                # 从total_count_text中提取数字进行对比
                import re
                total_numbers = re.findall(r'\d+', total_count_text)
                if total_numbers:
                    total_count_num = int(total_numbers[0])
                    if total_count_num == website_total_records:
                        logger.info(f"✅ 数据验证通过: T1({total_count_num}) = T2({website_total_records})")
                    else:
                        logger.warning(f"⚠️ 数据不一致: T1({total_count_num}) ≠ T2({website_total_records})")
            except:
                logger.debug("📊 无法进行数据对比验证")

    except Exception as e:
        logger.error(f"❌ 汇总数据写入异常: {e}")


async def extract_user_data_sequential(selector_executor: SelectorExecutor, target_month_str: str) -> tuple:
    """在顺序导航模式下提取用户数据（保持向后兼容）"""
    data, username, _ = await extract_user_data_sequential_enhanced(selector_executor, target_month_str)
    return data, username


async def write_batch_to_sheets(sheets_client: SheetsClient, batch_data: list, task_config: dict):
    """批量写入数据到Google Sheets"""
    try:
        if not batch_data:
            logger.warning("⚠️ 批量数据为空，跳过写入")
            return

        # 获取工作表配置
        spreadsheet_id = task_config.get('spreadsheet_id')
        target_sheet_name = task_config.get('target_sheet_name', 'kaipoke_performance_data')

        if not spreadsheet_id:
            logger.error("❌ 缺少spreadsheet_id配置")
            return

        # 🆕 使用正确的SheetsClient方法进行批量写入
        try:
            # 找到第一个空白行
            range_name = f"{target_sheet_name}!A:T"
            blank_row_range = sheets_client.find_first_blank_row(range_name)

            # 使用write_to_sheet方法写入数据
            result = sheets_client.write_to_sheet(
                spreadsheet_id=spreadsheet_id,
                range_name=blank_row_range.replace('A', 'A').replace(':T', f':T{len(batch_data)}'),
                values=batch_data
            )

            if result:
                logger.info(f"✅ 成功写入 {len(batch_data)} 行数据到Google Sheets ({target_sheet_name})")
            else:
                logger.error(f"❌ 写入 {len(batch_data)} 行数据到Google Sheets失败")

        except Exception as write_error:
            logger.error(f"❌ 写入操作失败: {write_error}")

            # 🆕 备用方案：分批写入
            logger.info("🔄 尝试分批写入...")
            chunk_size = 10
            for i in range(0, len(batch_data), chunk_size):
                chunk = batch_data[i:i + chunk_size]
                try:
                    chunk_range = sheets_client.find_first_blank_row(range_name)
                    result = sheets_client.write_to_sheet(
                        spreadsheet_id=spreadsheet_id,
                        range_name=chunk_range.replace('A', 'A').replace(':T', f':T{len(chunk)}'),
                        values=chunk
                    )
                    if result:
                        logger.info(f"✅ 分批写入成功: 第 {i//chunk_size + 1} 批，{len(chunk)} 行")
                    await asyncio.sleep(1)  # API限制保护
                except Exception as chunk_error:
                    logger.error(f"❌ 分批写入失败: {chunk_error}")

    except Exception as e:
        logger.error(f"❌ 批量写入Google Sheets异常: {e}")


async def extract_user_data_from_page_new_tab(main_page, user_index: int, target_month_str: str):
    """在新标签页中抽取用户数据 - 架构师级别的分页状态保持方案"""
    user_page = None
    try:
        logger.debug(f"开始在新标签页中抽取用户 {user_index + 1} 的数据...")

        # 🆕 在新标签页中打开用户详情
        user_page = await click_user_by_index_new_tab(main_page, user_index)
        if not user_page:
            raise Exception(f"无法在新标签页中打开用户 {user_index + 1}")

        # 🆕 在用户详情页面抽取数据
        logger.debug(f"📊 开始抽取用户 {user_index + 1} 的数据...")

        # 创建临时的selector_executor用于新标签页
        from core.selector_executor import SelectorExecutor
        temp_selector_executor = SelectorExecutor(user_page)

        # 获取用户名
        username = await get_user_name_from_page(temp_selector_executor)
        logger.debug(f"👤 用户名: {username}")

        # 检查用户是否有数据
        has_data = await check_user_has_data(temp_selector_executor)

        if not has_data:
            logger.info(f"📄 用户 {user_index + 1} ({username}) 无数据，创建占位行")
            # 创建占位行
            placeholder_data = [[username, target_month_str, "データなし", "", "", "", ""]]
            return placeholder_data, username

        # 抽取实际数据
        extracted_data = await extract_user_data(temp_selector_executor)

        if not extracted_data:
            logger.warning(f"⚠️ 用户 {user_index + 1} ({username}) 数据抽取为空，创建占位行")
            placeholder_data = [[username, target_month_str, "データなし", "", "", "", ""]]
            return placeholder_data, username

        logger.debug(f"✅ 用户 {user_index + 1} ({username}) 数据抽取完成: {len(extracted_data)} 行")
        return extracted_data, username

    except Exception as e:
        logger.error(f"❌ 用户 {user_index + 1} 数据抽取失败: {e}")
        # 返回错误占位行
        error_username = f"用户{user_index + 1}"
        error_data = [[error_username, target_month_str, "抽取失败", "", "", "", ""]]
        return error_data, error_username

    finally:
        # 🆕 关闭用户详情标签页
        if user_page:
            try:
                await user_page.close()
                logger.debug(f"✅ 已关闭用户 {user_index + 1} 的标签页")
            except Exception as e:
                logger.warning(f"⚠️ 关闭标签页失败: {e}")


async def click_user_by_index(page, user_index: int):
    """点击指定索引的用户 - 增强版本"""
    # 使用正确的用户选择器，注意tr从第2行开始（第1行是表头）
    row_index = user_index + 2  # 第2行是第一个用户，第3行是第二个用户，以此类推
    user_selector = f"#form > div.box-btn.mar-b-0.clearfix > table > tbody > tr:nth-child({row_index}) > td:nth-child(1) > a"

    try:
        # 🆕 首先检测是否在错误页面
        logger.debug("🔍 检测当前页面状态...")
        if await detect_and_handle_error_page(page):
            logger.warning("⚠️ 当前在错误页面，尝试恢复...")

            # 尝试恢复到用户列表
            recovery_success = await recover_from_error_page(page, page_state_manager)

            if not recovery_success:
                raise Exception("当前在错误页面且恢复失败，无法点击用户")
            else:
                logger.info("✅ 成功从错误页面恢复")

        # 🆕 验证用户列表页面状态
        logger.debug("🔍 验证用户列表页面状态...")

        # 🆕 等待表格加载
        try:
            await page.wait_for_selector("#form > div.box-btn.mar-b-0.clearfix > table > tbody > tr", timeout=10000)
        except:
            logger.warning("⚠️ 用户表格加载超时")

        user_table_rows = await page.locator("#form > div.box-btn.mar-b-0.clearfix > table > tbody > tr").count()
        logger.debug(f"📊 用户表格总行数: {user_table_rows}")

        # 🆕 修复行数计算逻辑
        if user_table_rows == 0:
            raise Exception("用户表格不存在或未加载")

        actual_user_count = max(0, user_table_rows - 1)  # 减去表头，但不能小于0
        logger.debug(f"📊 实际用户数量: {actual_user_count}")

        if user_index >= actual_user_count:  # 索引从0开始
            raise Exception(f"用户索引 {user_index + 1} 超出范围，当前只有 {actual_user_count} 个用户")

        # 🆕 等待并验证元素存在
        logger.debug(f"等待用户 {user_index + 1} 的链接元素: {user_selector}")
        await page.wait_for_selector(user_selector, timeout=15000)

        # 🆕 验证元素可见且可点击
        user_element = page.locator(user_selector)
        if not await user_element.is_visible():
            raise Exception(f"用户 {user_index + 1} 的链接不可见")

        # 🆕 获取用户名用于日志
        user_text = await user_element.text_content()
        logger.debug(f"准备点击用户: {user_text}")

        await user_element.click()
        logger.debug(f"成功点击用户 {user_index + 1} (行 {row_index})")

    except Exception as e:
        # 尝试备用选择器
        logger.warning(f"主选择器失败，尝试备用选择器: {e}")
        try:
            backup_selector = f"table tbody tr:nth-child({row_index}) td:nth-child(1) a"
            await page.wait_for_selector(backup_selector, timeout=10000)
            await page.locator(backup_selector).click()
            logger.debug(f"使用备用选择器成功点击用户 {user_index + 1}")
        except Exception as backup_error:
            logger.error(f"备用选择器也失败: {backup_error}")

            # 🆕 失败时的调试信息
            try:
                current_url = page.url
                user_table_count = await page.locator("#form > div.box-btn.mar-b-0.clearfix > table > tbody > tr").count()
                logger.debug(f"失败时调试信息:")
                logger.debug(f"  当前URL: {current_url}")
                logger.debug(f"  用户表格行数: {user_table_count}")
                logger.debug(f"  目标用户索引: {user_index + 1}")
            except:
                pass

            raise backup_error

    # 🆕 等待页面跳转并验证
    await page.wait_for_timeout(3000)

    # 🆕 验证是否成功跳转到用户详情页
    try:
        current_url = page.url
        if "MEM087101.do" in current_url:
            logger.debug(f"✅ 成功跳转到用户详情页: {current_url}")
        else:
            logger.warning(f"⚠️ 点击后URL未按预期变化: {current_url}")
    except:
        pass


async def write_data_to_sheets(sheets_client: SheetsClient, task_config: dict, extracted_data: list):
    """将数据写入Google Sheets（单个用户，已弃用）"""
    paste_range = sheets_client.find_first_blank_row(task_config.get('paste_range'))
    sheets_client.append_values(paste_range, extracted_data)


async def write_summary_data_to_sheets(sheets_client: SheetsClient, task_config: dict, total_count: str, website_data_count: int):
    """写入汇总数据到T1和T2位置"""
    try:
        # 写入T1位置：合计数（从.pager-btm p获取的页面显示信息）
        if 'total_count_cell' in task_config:
            t1_cell = task_config['total_count_cell']
            sheets_client.update_cell(t1_cell, total_count)
            logger.info(f"✅ T1位置写入成功: {t1_cell} = {total_count}")

        # 写入T2位置：网站实际数据总量（用于与T1对比验证）
        if 'user_count_cell' in task_config:
            t2_cell = task_config['user_count_cell']
            sheets_client.update_cell(t2_cell, str(website_data_count))
            logger.info(f"✅ T2位置写入成功: {t2_cell} = {website_data_count} 条网站数据")

        # API限制保护
        import asyncio
        await asyncio.sleep(1)

    except Exception as e:
        logger.error(f"❌ 汇总数据写入失败: {e}")


async def batch_write_to_sheets(sheets_client: SheetsClient, task_config: dict, batch_data: list):
    """批量写入数据到Google Sheets - 架构师级别的API优化"""
    if not batch_data:
        logger.warning("批量数据为空，跳过写入")
        return

    try:
        # 获取写入范围
        paste_range = sheets_client.find_first_blank_row(task_config.get('paste_range'))

        # 批量写入所有数据
        sheets_client.append_values(paste_range, batch_data)

        logger.info(f"✅ 批量写入成功: {len(batch_data)} 行数据写入到 {paste_range}")

        # API限制保护：写入后短暂等待
        import asyncio
        await asyncio.sleep(1)  # 1秒等待，避免API限制

    except Exception as e:
        logger.error(f"❌ 批量写入失败: {e}")

        # 错误恢复：尝试分批写入
        logger.info("🔄 尝试分批写入恢复...")
        chunk_size = 10  # 更小的批次

        for i in range(0, len(batch_data), chunk_size):
            chunk = batch_data[i:i + chunk_size]
            try:
                paste_range = sheets_client.find_first_blank_row(task_config.get('paste_range'))
                sheets_client.append_values(paste_range, chunk)
                logger.info(f"✅ 分批写入成功: 第 {i//chunk_size + 1} 批，{len(chunk)} 行")
                await asyncio.sleep(2)  # 更长的等待时间
            except Exception as chunk_e:
                logger.error(f"❌ 分批写入也失败: {chunk_e}")
                # 可以考虑保存到本地文件作为备份


async def enhanced_return_to_user_list(selector_executor: SelectorExecutor, current_page_number: int) -> bool:
    """增强的返回用户列表方法 - 确保保持分页状态"""
    page = selector_executor.page
    current_url = page.url
    logger.debug(f"增强返回: 当前页码 {current_page_number}, URL: {current_url}")

    try:
        # 🆕 优先使用页面状态管理器
        if hasattr(page_state_manager, 'user_list_base_url') and page_state_manager.user_list_base_url:
            logger.debug(f"📄 使用状态管理器URL: {page_state_manager.user_list_base_url}")
            await page.goto(page_state_manager.user_list_base_url, wait_until='networkidle', timeout=15000)
            await page.wait_for_timeout(3000)

            # 验证返回是否成功
            if "MEM087001.do" in page.url and not await detect_and_handle_error_page(page):
                logger.debug("✅ 状态管理器返回成功")
                return True

        # 🆕 备用方案：使用传统返回方法
        logger.debug("📄 使用传统返回方法")
        await return_to_user_list(selector_executor)

        # 验证返回结果
        await page.wait_for_timeout(2000)
        if "MEM087001.do" in page.url and not await detect_and_handle_error_page(page):
            logger.debug("✅ 传统返回方法成功")
            return True
        else:
            logger.warning("⚠️ 传统返回方法可能失败")
            return False

    except Exception as e:
        logger.error(f"❌ 增强返回方法失败: {e}")
        return False


async def return_to_user_list(selector_executor: SelectorExecutor):
    """返回用户列表页面 - 架构师级别的页面状态保持方案"""
    page = selector_executor.page

    # 记录当前页面URL（调试用）
    current_url = page.url
    logger.debug(f"返回用户列表前的URL: {current_url}")

    try:
        # 🆕 架构师方案：使用返回按钮而不是浏览器后退
        # 这样可以保持当前分页状态

        # 方案1: 尝试点击页面上的返回按钮
        return_button_selectors = [
            "input[value='戻る']",  # 日文返回按钮
            "input[type='button'][value*='戻']",  # 包含"戻"的按钮
            ".btn-back",  # 返回按钮类
            ".back-button",  # 返回按钮类
            "button[onclick*='back']",  # 包含back的按钮
            "a[href*='back']",  # 包含back的链接
        ]

        button_clicked = False
        for selector in return_button_selectors:
            try:
                button_element = page.locator(selector)
                if await button_element.count() > 0:
                    logger.debug(f"找到返回按钮: {selector}")
                    await button_element.first.click()
                    button_clicked = True
                    logger.debug("✅ 使用返回按钮返回用户列表")
                    break
            except Exception as e:
                logger.debug(f"返回按钮 {selector} 点击失败: {e}")
                continue

        # 方案2: 如果没有找到返回按钮，使用智能导航
        if not button_clicked:
            logger.debug("未找到返回按钮，使用智能导航方案")

            # 🆕 优先使用页面状态管理器的保存URL
            if hasattr(page_state_manager, 'user_list_base_url') and page_state_manager.user_list_base_url:
                logger.debug(f"📄 使用状态管理器保存的URL: {page_state_manager.user_list_base_url}")
                await page.goto(page_state_manager.user_list_base_url, wait_until='networkidle', timeout=15000)
                logger.debug("✅ 使用状态管理器URL返回用户列表")
            elif "MEM087101.do" in current_url:
                # 🆕 改进的URL构造逻辑：更精确地保持分页状态
                import urllib.parse
                parsed_url = urllib.parse.urlparse(current_url)
                query_params = urllib.parse.parse_qs(parsed_url.query)

                # 构造返回用户列表的URL，保持分页参数
                base_url = current_url.split('MEM087101.do')[0]
                user_list_url = f"{base_url}MEM087001.do"

                # 🆕 保持所有重要的查询参数，特别是分页相关参数
                preserved_params = []
                important_params = [
                    'conversationContext', 'page', 'pageNo', 'currentPage',
                    'pageIndex', 'offset', 'limit', 'start', 'size'
                ]

                for key, values in query_params.items():
                    if key in important_params:
                        for value in values:
                            preserved_params.append(f"{key}={value}")

                if preserved_params:
                    user_list_url += "?" + "&".join(preserved_params)

                logger.debug(f"构造的用户列表URL: {user_list_url}")

                # 导航到构造的URL
                await page.goto(user_list_url, wait_until='networkidle', timeout=15000)
                logger.debug("✅ 使用智能导航返回用户列表")

                # 🆕 智能导航后的页面加载验证
                logger.debug("📄 等待页面完全加载...")
                await page.wait_for_timeout(5000)  # 增加等待时间

                # 🆕 等待关键元素加载
                try:
                    await page.wait_for_selector("#form", timeout=10000)
                    logger.debug("✅ #form 元素已加载")
                except:
                    logger.warning("⚠️ #form 元素加载超时")

                # 🆕 等待用户表格加载
                try:
                    await page.wait_for_selector("#form > div.box-btn.mar-b-0.clearfix > table > tbody > tr", timeout=10000)
                    logger.debug("✅ 用户表格已加载")
                except:
                    logger.warning("⚠️ 用户表格加载超时")
            else:
                # 备用方案：使用浏览器后退（但会丢失分页状态）
                logger.warning("⚠️ 使用备用方案：浏览器后退（可能丢失分页状态）")
                await page.go_back()
                logger.debug("使用浏览器后退返回用户列表")

        # 等待页面加载
        await page.wait_for_timeout(2000)

        # 记录返回后的页面URL（调试用）
        new_url = page.url
        logger.debug(f"返回用户列表后的URL: {new_url}")

        # 🆕 增强的错误页面检测和恢复
        max_recovery_attempts = 3
        for recovery_attempt in range(max_recovery_attempts):
            if await detect_and_handle_error_page(page):
                logger.warning(f"⚠️ 返回后进入了错误页面，尝试恢复 (第{recovery_attempt + 1}次)...")

                # 尝试从错误页面恢复
                recovery_success = await recover_from_error_page(page, page_state_manager)

                if not recovery_success:
                    if recovery_attempt == max_recovery_attempts - 1:
                        logger.error("❌ 所有错误页面恢复尝试都失败")
                        raise Exception("返回用户列表后进入错误页面且恢复失败")
                    else:
                        logger.warning(f"⚠️ 第{recovery_attempt + 1}次恢复失败，等待后重试...")
                        await page.wait_for_timeout(2000)
                        continue
                else:
                    logger.info(f"✅ 第{recovery_attempt + 1}次尝试成功从错误页面恢复到用户列表")
                    # 更新URL记录
                    new_url = page.url
                    logger.debug(f"恢复后的URL: {new_url}")
                    break
            else:
                # 没有错误页面，正常退出
                break

        # 🆕 改进的用户列表页面验证
        user_list_indicators = [
            "#form > div.box-btn.mar-b-0.clearfix > table > tbody > tr",  # 用户表格行
            ".color-neutral a",  # 用户链接
            "table tr td a",  # 表格中的链接
        ]

        user_list_exists = False
        for indicator in user_list_indicators:
            count = await page.locator(indicator).count()
            if count > 1:  # 至少有表头+数据行
                user_list_exists = True
                logger.debug(f"✅ 成功返回用户列表页面，检测到 {count} 个 {indicator}")
                break

        if user_list_exists:
            # 🆕 额外验证：检查是否保持了分页状态
            current_page_info = ""
            try:
                pager_element = page.locator(".pager-btm")
                if await pager_element.count() > 0:
                    current_page_info = await pager_element.text_content()
                    logger.debug(f"📄 当前分页状态: {current_page_info}")
            except Exception:
                pass
        else:
            logger.warning("⚠️ 可能没有正确返回用户列表页面")
            # 🆕 调试信息：记录当前页面状态
            current_url = page.url
            logger.debug(f"📄 当前页面URL: {current_url}")

            # 检查页面上是否有其他元素
            form_exists = await page.locator("#form").count() > 0
            logger.debug(f"📄 #form 元素存在: {form_exists}")

    except Exception as e:
        logger.error(f"返回用户列表失败: {e}")
        raise


async def recover_from_error_page(page, target_page: int) -> bool:
    """从错误页面恢复到指定分页的用户列表页面 - 架构师级别的错误恢复"""
    try:
        logger.info(f"🔄 开始错误页面恢复，目标页面: {target_page}")

        # 策略1: 直接构造目标页面URL
        try:
            # 获取基础URL（从当前URL或者使用已知的基础URL）
            base_url = "https://r.kaipoke.biz/kaipokebiz/business/plan_actual/MEM087001.do"

            # 🆕 构造带分页参数的URL
            if target_page > 1:
                # 这里需要根据实际的分页参数格式调整
                target_url = f"{base_url}?page={target_page}"
            else:
                target_url = base_url

            logger.debug(f"🔄 尝试直接导航到目标页面: {target_url}")
            await page.goto(target_url, wait_until='networkidle', timeout=15000)
            await page.wait_for_timeout(3000)

            # 验证是否成功到达用户列表页面
            user_count = await get_total_users_on_current_page(page)
            if user_count > 0:
                logger.info(f"✅ 直接导航恢复成功，页面有 {user_count} 个用户")
                return True

        except Exception as e:
            logger.warning(f"直接导航恢复失败: {e}")

        # 策略2: 多次浏览器后退
        try:
            logger.info("🔄 尝试浏览器后退恢复策略")
            for i in range(5):  # 增加后退次数
                await page.go_back()
                await page.wait_for_timeout(2000)

                # 检查是否到达用户列表页面
                user_count = await get_total_users_on_current_page(page)
                if user_count > 0:
                    logger.info(f"✅ 后退恢复成功: 后退{i+1}次后到达用户列表，有 {user_count} 个用户")
                    return True
                elif user_count == -1:
                    logger.debug(f"后退{i+1}次仍在错误页面，继续后退")
                    continue

            logger.warning("⚠️ 后退恢复失败: 多次后退未找到有效用户列表")

        except Exception as e:
            logger.error(f"后退恢复策略异常: {e}")

        # 策略3: 重新从设施列表开始（最后的手段）
        try:
            logger.warning("🔄 使用最后手段：重新从设施列表开始")
            facility_list_url = "https://r.kaipoke.biz/kaipokebiz/common/COM020101.do"
            await page.goto(facility_list_url, wait_until='networkidle', timeout=15000)
            logger.info("⚠️ 已返回设施列表，需要重新选择设施和筛选条件")
            return False  # 返回False表示需要重新开始流程

        except Exception as e:
            logger.error(f"重新开始策略也失败: {e}")

        return False

    except Exception as e:
        logger.error(f"错误页面恢复失败: {e}")
        return False


async def enhanced_return_to_user_list(selector_executor: SelectorExecutor, current_page: int) -> bool:
    """增强的返回用户列表策略 - 确保保持正确的分页状态"""
    page = selector_executor.page

    try:
        logger.debug(f"🔄 增强返回策略开始，目标页面: {current_page}")

        # 🆕 策略1: 智能URL构造 - 保持分页状态
        current_url = page.url
        if "MEM087101.do" in current_url:
            # 解析当前URL的参数
            import urllib.parse
            parsed_url = urllib.parse.urlparse(current_url)
            query_params = urllib.parse.parse_qs(parsed_url.query)

            # 构造用户列表URL，保持所有重要参数
            base_url = current_url.split('MEM087101.do')[0]
            user_list_url = f"{base_url}MEM087001.do"

            # 保持所有查询参数（特别是分页相关的）
            if query_params:
                preserved_params = []
                for key, values in query_params.items():
                    for value in values:
                        preserved_params.append(f"{key}={value}")

                if preserved_params:
                    user_list_url += "?" + "&".join(preserved_params)

            logger.debug(f"🔄 构造的用户列表URL: {user_list_url}")

            # 导航到构造的URL
            await page.goto(user_list_url, wait_until='networkidle', timeout=15000)
            await page.wait_for_timeout(3000)

            # 🆕 验证返回是否成功
            user_count = await get_total_users_on_current_page(page)
            if user_count > 0:
                logger.debug(f"✅ 智能URL构造返回成功，页面有 {user_count} 个用户")
                return True
            elif user_count == -1:
                logger.warning("⚠️ 智能URL构造后仍在错误页面")
                # 等待一下，可能页面还在加载
                await page.wait_for_timeout(5000)
                user_count = await get_total_users_on_current_page(page)
                if user_count > 0:
                    logger.debug(f"✅ 延迟验证成功，页面有 {user_count} 个用户")
                    return True

        # 🆕 策略2: 使用返回按钮（如果存在）
        return_button_selectors = [
            "input[value='戻る']",
            "input[type='button'][value*='戻']",
            ".btn-back",
            ".back-button",
        ]

        for selector in return_button_selectors:
            try:
                button_element = page.locator(selector)
                if await button_element.count() > 0:
                    logger.debug(f"🔄 尝试使用返回按钮: {selector}")
                    await button_element.first.click()
                    await page.wait_for_timeout(3000)

                    user_count = await get_total_users_on_current_page(page)
                    if user_count > 0:
                        logger.debug(f"✅ 返回按钮策略成功，页面有 {user_count} 个用户")
                        return True
                    break
            except Exception:
                continue

        # 🆕 策略3: 浏览器后退（最后手段）
        logger.debug("🔄 使用浏览器后退作为最后手段")
        await page.go_back()
        await page.wait_for_timeout(3000)

        user_count = await get_total_users_on_current_page(page)
        if user_count > 0:
            logger.debug(f"✅ 浏览器后退成功，页面有 {user_count} 个用户")
            return True

        logger.warning("⚠️ 所有增强返回策略都失败")
        return False

    except Exception as e:
        logger.error(f"增强返回策略异常: {e}")
        return False


async def ensure_ready_for_pagination(selector_executor: SelectorExecutor, current_page_number: int) -> bool:
    """确保页面准备好进行分页操作"""
    page = selector_executor.page

    try:
        logger.debug(f"📄 验证第{current_page_number}页是否准备好分页...")

        # 1. 检查当前页面URL
        current_url = page.url
        logger.debug(f"当前URL: {current_url}")

        # 2. 如果在用户详情页面，先返回用户列表
        if "MEM087101.do" in current_url:
            logger.info("🔄 检测到在用户详情页面，先返回用户列表")
            return_success = await enhanced_return_to_user_list(selector_executor, current_page_number)
            if not return_success:
                logger.warning("⚠️ 返回用户列表失败")
                return False
            await page.wait_for_timeout(3000)

        # 3. 验证是否在用户列表页面
        if "MEM087001.do" not in page.url:
            logger.warning(f"⚠️ 不在用户列表页面: {page.url}")
            return False

        # 4. 检查是否有错误页面
        if await detect_and_handle_error_page(page):
            logger.warning("⚠️ 检测到错误页面")
            return False

        # 5. 验证用户列表表格是否存在
        user_table_exists = await page.locator("#form > div.box-btn.mar-b-0.clearfix > table > tbody > tr").count() > 1
        if not user_table_exists:
            logger.warning("⚠️ 用户列表表格不存在或为空")
            return False

        logger.debug("✅ 页面已准备好进行分页")
        return True

    except Exception as e:
        logger.error(f"❌ 页面分页准备验证失败: {e}")
        return False


async def safe_return_to_user_list(selector_executor: SelectorExecutor):
    """安全地返回用户列表页面（错误恢复用）"""
    page = selector_executor.page

    try:
        await return_to_user_list(selector_executor)
    except Exception as e:
        logger.warning(f"标准返回用户列表失败: {e}")

        # 简化的恢复策略：多次浏览器后退
        try:
            logger.info("尝试恢复策略: 多次浏览器后退")
            for i in range(3):
                await page.go_back()
                await page.wait_for_timeout(1000)

                # 检查是否到达用户列表页面
                user_list_exists = await page.locator('.color-neutral a, table tr td a').count() > 0
                if user_list_exists:
                    logger.info(f"恢复策略成功: 后退{i+1}次后到达用户列表")
                    return

            logger.warning("恢复策略失败: 多次后退未找到用户列表")

        except Exception as e2:
            logger.error(f"恢复策略异常: {e2}")
            # 抛出异常，让上层处理
            raise Exception("用户列表返回失败")


async def ensure_ready_for_pagination(selector_executor: SelectorExecutor, current_page: int) -> bool:
    """确保页面状态适合进行分页操作 - 架构师级别的状态管理"""
    page = selector_executor.page

    try:
        logger.debug(f"🔍 验证页面是否准备好进行分页，当前应在第 {current_page} 页")

        # 🆕 步骤1: 检查当前页面状态
        current_url = page.url
        logger.debug(f"📄 当前URL: {current_url}")

        # 🆕 步骤2: 如果在用户详情页面，先返回用户列表
        if "MEM087101.do" in current_url:
            logger.info("🔄 检测到在用户详情页面，先返回用户列表")
            return_success = await enhanced_return_to_user_list(selector_executor, current_page)
            if not return_success:
                logger.error("❌ 从用户详情页面返回失败")
                return False

            # 重新获取URL
            current_url = page.url
            logger.debug(f"📄 返回后URL: {current_url}")

        # 🆕 步骤3: 验证是否在正确的用户列表页面
        if "MEM087001.do" not in current_url:
            logger.warning(f"⚠️ 不在用户列表页面: {current_url}")

            # 尝试恢复到用户列表页面
            recovery_success = await recover_from_error_page(page, current_page)
            if not recovery_success:
                return False

        # 🆕 步骤4: 验证页面内容
        user_count = await get_total_users_on_current_page(page)
        if user_count == -1:
            logger.warning("⚠️ 检测到错误页面状态")
            recovery_success = await recover_from_error_page(page, current_page)
            if not recovery_success:
                return False

            # 重新检查
            user_count = await get_total_users_on_current_page(page)
            if user_count <= 0:
                logger.error("❌ 恢复后仍无法获取有效用户数据")
                return False

        # 🆕 步骤5: 验证分页元素存在
        pager_exists = await page.locator(".pager-btm").count() > 0
        if not pager_exists:
            logger.warning("⚠️ 分页元素不存在，可能不在正确的页面")
            return False

        # 🆕 步骤6: 验证用户表格存在
        user_table_exists = await page.locator("#form > div.box-btn.mar-b-0.clearfix > table > tbody > tr").count() > 1
        if not user_table_exists:
            logger.warning("⚠️ 用户表格不存在或为空")
            return False

        logger.debug(f"✅ 页面状态验证通过，准备进行分页操作")
        return True

    except Exception as e:
        logger.error(f"页面状态验证异常: {e}")
        return False


async def detect_and_handle_error_page(page) -> bool:
    """检测并处理错误页面 - 修复版本，减少误判"""
    try:
        current_url = page.url
        logger.debug(f"🔍 检测页面状态: {current_url}")

        # 🆕 如果是正确的用户列表页面URL，先给页面充分时间加载
        if "MEM087001.do" in current_url:
            logger.debug("📄 检测到用户列表页面URL，等待页面充分加载...")
            try:
                # 增加等待时间，确保页面完全加载
                await page.wait_for_load_state('networkidle', timeout=15000)
                await page.wait_for_timeout(5000)  # 增加等待时间
                logger.debug("✅ 页面加载等待完成")
            except Exception as load_error:
                logger.debug(f"⏰ 页面加载等待超时: {load_error}，继续检测")

        # 🔧 修复：更严格的错误页面检测，大幅减少误判
        # 只检测非常明确的错误指示器
        critical_error_indicators = [
            "404 not found",
            "500 internal server error",
            "502 bad gateway",
            "503 service unavailable",
            "session expired",
            "session timeout",
            "access denied",
            "unauthorized",
            "エラーが発生しました",
            "システムエラー",
            "予期しないエラー",
            "ページが見つかりません"
        ]

        # 检查URL中是否包含明确的错误指示器
        url_has_error = any(indicator in current_url.lower() for indicator in ["error", "404", "500", "502", "503"])

        # 🔧 修复：更精确的页面内容错误检测
        page_text = ""
        content_has_error = False
        try:
            # 只检查页面标题和主要内容区域，避免检查整个body
            title_text = await page.text_content("title") or ""
            main_content = await page.text_content("body") or ""
            page_text = (title_text + " " + main_content).lower()

            # 只检测明确的错误消息，避免误判
            content_has_error = any(
                indicator in page_text
                for indicator in critical_error_indicators
            )
        except Exception as text_error:
            logger.debug(f"页面文本检查失败: {text_error}")

        # 🔧 修复：检查关键页面元素存在性
        form_exists = await page.locator("#form").count() > 0
        table_exists = await page.locator("#form > div.box-btn.mar-b-0.clearfix > table").count() > 0

        # 🔧 修复：非常宽松的判断逻辑 - 只有非常明确的错误才判断为错误页面
        is_error_page = url_has_error or content_has_error

        # 🔧 修复：如果URL正确，几乎不判断为错误页面
        if "MEM087001.do" in current_url:
            logger.debug(f"📄 用户列表页面状态检查: 表单={form_exists}, 表格={table_exists}")

            # 只有在明确检测到错误内容时才判断为错误页面
            if is_error_page:
                logger.warning(f"⚠️ 在用户列表页面检测到明确错误内容")
                logger.warning(f"   URL错误: {url_has_error}")
                logger.warning(f"   内容错误: {content_has_error}")
                return True
            else:
                # 即使没有表格也不算错误，可能是无数据或正在加载的正常状态
                logger.debug("✅ 用户列表页面状态正常（即使可能缺少某些元素）")
                return False

        # 对于非用户列表页面，进行正常的错误检测
        if is_error_page:
            logger.warning(f"⚠️ 检测到明确的错误页面:")
            logger.warning(f"   当前URL: {current_url}")
            logger.warning(f"   URL错误: {url_has_error}")
            logger.warning(f"   内容错误: {content_has_error}")
            logger.warning(f"   表单存在: {form_exists}")
            logger.warning(f"   表格存在: {table_exists}")
            return True
        else:
            logger.debug("✅ 页面状态正常")
            return False

    except Exception as e:
        logger.error(f"错误页面检测失败: {e}")
        return False


async def recover_from_error_page(page, page_state_manager) -> bool:
    """从错误页面恢复到用户列表"""
    try:
        logger.info("🔄 尝试从错误页面恢复...")

        # 策略1: 使用保存的页面状态
        if hasattr(page_state_manager, 'user_list_base_url') and page_state_manager.user_list_base_url:
            logger.debug("📄 使用保存的页面状态恢复")
            try:
                await page.goto(page_state_manager.user_list_base_url, wait_until='networkidle', timeout=15000)
                await page.wait_for_timeout(5000)

                # 🆕 等待关键元素加载
                await page.wait_for_selector("#form", timeout=10000)
                await page.wait_for_selector("#form > div.box-btn.mar-b-0.clearfix > table > tbody", timeout=10000)

                # 验证恢复是否成功
                if not await detect_and_handle_error_page(page):
                    logger.info("✅ 使用保存状态成功恢复")
                    return True
            except Exception as e:
                logger.warning(f"⚠️ 使用保存状态恢复失败: {e}")

        # 策略2: 导航到基础用户列表URL，尝试保持分页状态
        logger.debug("📄 尝试导航到基础用户列表URL")
        current_url = page.url
        if '/kaipokebiz/' in current_url:
            base_url = current_url.split('/kaipokebiz/')[0] + '/kaipokebiz/'
            user_list_url = base_url + "business/plan_actual/MEM087001.do"

            # 🆕 如果有保存的分页参数，尝试添加到URL中
            if hasattr(page_state_manager, 'pagination_params') and page_state_manager.pagination_params:
                preserved_params = []
                important_params = ['conversationContext', 'page', 'pageNo', 'currentPage']

                for key, values in page_state_manager.pagination_params.items():
                    if key in important_params:
                        for value in values:
                            preserved_params.append(f"{key}={value}")

                if preserved_params:
                    user_list_url += "?" + "&".join(preserved_params)
                    logger.debug(f"📄 添加分页参数到基础URL: {user_list_url}")

            try:
                await page.goto(user_list_url, wait_until='networkidle', timeout=15000)
                await page.wait_for_timeout(5000)

                # 🆕 等待关键元素加载
                await page.wait_for_selector("#form", timeout=10000)
                await page.wait_for_selector("#form > div.box-btn.mar-b-0.clearfix > table > tbody", timeout=10000)

                # 验证恢复是否成功
                if not await detect_and_handle_error_page(page):
                    logger.info("✅ 使用基础URL成功恢复")
                    return True
            except Exception as e:
                logger.warning(f"⚠️ 使用基础URL恢复失败: {e}")

        # 策略3: 浏览器后退
        logger.debug("📄 尝试浏览器后退")
        await page.go_back()
        await page.wait_for_timeout(3000)

        # 验证恢复是否成功
        if not await detect_and_handle_error_page(page):
            logger.info("✅ 使用浏览器后退成功恢复")
            return True

        logger.error("❌ 所有恢复策略都失败了")
        return False

    except Exception as e:
        logger.error(f"错误页面恢复失败: {e}")
        return False


class PageStateManager:
    """页面状态管理器 - 架构师级别的状态保持解决方案"""

    def __init__(self):
        self.current_page_url = None
        self.current_page_info = None
        self.user_list_base_url = None
        self.pagination_params = {}
        self.current_page_number = 1  # 🆕 添加当前页码跟踪
        self.total_pages = 0  # 🆕 添加总页数跟踪

    async def save_current_page_state(self, page):
        """保存当前页面状态"""
        try:
            self.current_page_url = page.url

            # 🆕 提取并保存当前页码
            try:
                pager_element = page.locator(".pager-btm")
                if await pager_element.count() > 0:
                    pager_text = await pager_element.text_content()
                    self.current_page_info = pager_text

                    # 解析页码信息，例如 "1/5页" 或 "第1页/共5页"
                    import re
                    page_match = re.search(r'(\d+)[/／](\d+)', pager_text)
                    if page_match:
                        self.current_page_number = int(page_match.group(1))
                        self.total_pages = int(page_match.group(2))
                        logger.debug(f"📄 解析页码: 第{self.current_page_number}页/共{self.total_pages}页")
                    else:
                        # 尝试其他格式
                        page_match = re.search(r'第(\d+)页.*共(\d+)页', pager_text)
                        if page_match:
                            self.current_page_number = int(page_match.group(1))
                            self.total_pages = int(page_match.group(2))
                            logger.debug(f"📄 解析页码: 第{self.current_page_number}页/共{self.total_pages}页")
            except Exception as e:
                logger.debug(f"页码解析失败: {e}")

            # 解析URL参数
            import urllib.parse
            parsed_url = urllib.parse.urlparse(self.current_page_url)
            self.pagination_params = urllib.parse.parse_qs(parsed_url.query)

            # 🆕 保存用户列表基础URL，确保包含分页参数
            if "MEM087001.do" in self.current_page_url:
                self.user_list_base_url = self.current_page_url
                logger.debug(f"📄 保存用户列表URL: {self.user_list_base_url}")

            logger.debug(f"📄 已保存页面状态: {self.current_page_info}")

        except Exception as e:
            logger.warning(f"保存页面状态失败: {e}")

    async def restore_page_state(self, page):
        """恢复页面状态"""
        try:
            if self.user_list_base_url:
                logger.debug(f"📄 恢复到保存的页面状态: {self.user_list_base_url}")
                await page.goto(self.user_list_base_url, wait_until='networkidle', timeout=15000)

                # 🆕 增强的页面加载等待机制
                logger.debug("📄 等待页面完全加载...")
                await page.wait_for_timeout(5000)  # 基础等待

                # 🆕 等待关键元素逐步加载
                loading_steps = [
                    ("#form", "主表单"),
                    ("#form > div.box-btn.mar-b-0.clearfix", "按钮区域"),
                    ("#form > div.box-btn.mar-b-0.clearfix > table", "用户表格"),
                    ("#form > div.box-btn.mar-b-0.clearfix > table > tbody", "表格主体"),
                ]

                for selector, description in loading_steps:
                    try:
                        await page.wait_for_selector(selector, timeout=10000)
                        logger.debug(f"✅ {description} 已加载")
                    except:
                        logger.warning(f"⚠️ {description} 加载超时: {selector}")

                # 🆕 额外等待确保数据完全加载
                await page.wait_for_timeout(3000)

                # 🆕 验证是否成功返回用户列表页面
                user_list_indicators = [
                    "#form > div.box-btn.mar-b-0.clearfix > table > tbody > tr",  # 用户表格行
                    ".color-neutral a",  # 用户链接
                    "table tr td a",  # 表格中的链接
                ]

                page_restored = False
                for indicator in user_list_indicators:
                    count = await page.locator(indicator).count()
                    logger.debug(f"📊 检查 {indicator}: {count} 个元素")
                    if count > 1:  # 至少有表头+数据
                        page_restored = True
                        logger.debug(f"✅ 页面状态恢复成功，检测到用户列表: {indicator} ({count}个)")
                        break

                if page_restored:
                    # 验证分页信息
                    new_page_info = ""
                    pager_element = page.locator(".pager-btm")
                    if await pager_element.count() > 0:
                        new_page_info = await pager_element.text_content()
                        logger.debug(f"📄 当前分页状态: {new_page_info}")

                    return True
                else:
                    logger.warning("⚠️ 页面状态恢复失败，未检测到用户列表")

                    # 🆕 失败时的详细调试信息
                    current_url = page.url
                    form_exists = await page.locator("#form").count() > 0
                    table_exists = await page.locator("table").count() > 0
                    logger.debug(f"📄 调试信息: URL={current_url}, form={form_exists}, table={table_exists}")

                    return False

            logger.debug("📄 没有保存的页面状态可恢复")
            return False

        except Exception as e:
            logger.error(f"恢复页面状态失败: {e}")
            return False

# 全局页面状态管理器
page_state_manager = PageStateManager()


async def verify_user_list_page_state(selector_executor: SelectorExecutor) -> bool:
    """验证当前是否在正确的用户列表页面状态"""
    try:
        page = selector_executor.page
        current_url = page.url

        logger.debug(f"验证页面状态，当前URL: {current_url}")

        # 检查关键页面元素
        checks = [
            ("#form > div.box-btn.mar-b-0.clearfix > table > tbody", "用户列表表格"),
            ("#form", "主表单"),
        ]

        for selector, description in checks:
            element_count = await page.locator(selector).count()
            if element_count == 0:
                logger.warning(f"⚠️ 页面状态验证失败：缺少{description} ({selector})")
                return False
            logger.debug(f"✅ 页面状态验证通过：{description}")

        # 检查是否在用户详情页面（这才是我们要避免的）
        user_detail_indicators = [
            "#tableData",  # 用户详情数据表格
            "table[id*='tableData']",  # 备用用户详情表格选择器
        ]

        for indicator in user_detail_indicators:
            if await page.locator(indicator).count() > 0:
                logger.warning(f"⚠️ 检测到用户详情页面元素，不应在此页面执行分页: {indicator}")
                return False

        # 确保用户列表表格存在（这是正确页面的标志）
        user_list_table = await page.locator("#form > div.box-btn.mar-b-0.clearfix > table > tbody > tr").count()
        if user_list_table <= 1:  # 只有表头，没有用户数据
            logger.warning("⚠️ 用户列表表格为空或只有表头")
            return False

        logger.debug("✅ 页面状态验证完全通过")
        return True

    except Exception as e:
        logger.error(f"页面状态验证异常: {e}")
        return False


async def diagnose_pagination_failure(page, before_info: dict, after_info: dict):
    """诊断分页失败的原因"""
    logger.info("🔍 诊断分页失败原因...")

    # 检查下一页按钮状态
    next_buttons = [
        ".pager-btm .next02 a",
        ".pager-btm .next a",
        ".next02 a",
        ".next a"
    ]

    for selector in next_buttons:
        try:
            button_count = await page.locator(selector).count()
            if button_count > 0:
                button = page.locator(selector).first
                is_visible = await button.is_visible()
                is_enabled = await button.is_enabled()
                href = await button.get_attribute("href") or ""
                text = await button.text_content() or ""

                logger.info(f"📄 按钮 {selector}:")
                logger.info(f"   数量: {button_count}, 可见: {is_visible}, 可用: {is_enabled}")
                logger.info(f"   href: '{href}', 文本: '{text}'")

                # 检查是否是月份相关按钮
                if "month" in href.lower() or "date" in href.lower():
                    logger.warning(f"⚠️ 该按钮可能是月份导航按钮: {href}")
            else:
                logger.info(f"📄 按钮 {selector}: 未找到")
        except Exception as e:
            logger.error(f"检查按钮 {selector} 失败: {e}")

    # 检查页面是否有JavaScript错误
    try:
        # 检查控制台错误（如果可能）
        logger.info("📄 检查页面状态...")

        # 检查是否有加载指示器
        loading_indicators = [".loading", ".spinner", "[data-loading]"]
        for indicator in loading_indicators:
            if await page.locator(indicator).count() > 0:
                logger.warning(f"⚠️ 发现加载指示器: {indicator} - 页面可能仍在加载")

        # 检查分页区域
        pager_area = await page.locator(".pager-btm").count()
        if pager_area == 0:
            logger.warning("⚠️ 分页区域消失 - 可能跳转到了其他页面")

    except Exception as e:
        logger.error(f"页面状态检查失败: {e}")

    # 对比前后状态
    logger.info("📊 状态对比:")
    logger.info(f"   URL: '{before_info.get('url', '')}' -> '{after_info.get('url', '')}'")
    logger.info(f"   分页文本: '{before_info.get('pager_text', '')}' -> '{after_info.get('pager_text', '')}'")
    logger.info(f"   第一个用户: '{before_info.get('first_user', '')}' -> '{after_info.get('first_user', '')}'")
    logger.info(f"   用户数量: {before_info.get('user_count', 0)} -> {after_info.get('user_count', 0)}")


async def get_current_page_info(page) -> dict:
    """获取当前页面的关键信息用于分页验证"""
    try:
        page_info = {}

        # 获取分页信息文本
        pager_element = page.locator(".pager-btm")
        if await pager_element.count() > 0:
            page_info['pager_text'] = await pager_element.text_content()
        else:
            page_info['pager_text'] = ""

        # 获取用户列表的第一个用户名（作为页面内容标识）
        first_user_element = page.locator("#form > div.box-btn.mar-b-0.clearfix > table > tbody > tr:nth-child(2) > td:nth-child(1) > a")
        if await first_user_element.count() > 0:
            page_info['first_user'] = await first_user_element.text_content()
        else:
            page_info['first_user'] = ""

        # 获取用户总数
        user_rows = await page.locator("#form > div.box-btn.mar-b-0.clearfix > table > tbody > tr").count()
        page_info['user_count'] = max(0, user_rows - 1)  # 减去表头

        # 获取URL参数中的页码信息（如果有）
        current_url = page.url
        page_info['url'] = current_url

        # 尝试从URL中提取页码
        import re
        page_match = re.search(r'[?&]page=(\d+)', current_url)
        if page_match:
            page_info['url_page'] = int(page_match.group(1))
        else:
            page_info['url_page'] = None

        return page_info

    except Exception as e:
        logger.error(f"获取页面信息失败: {e}")
        return {}


async def navigate_to_next_page(selector_executor: SelectorExecutor) -> bool:
    """导航到下一页，返回是否成功 - 架构师级别的可靠验证"""
    try:
        page = selector_executor.page
        logger.info("📄 开始分页导航...")

        # 避免在用户详情页面执行分页
        if await page.locator("#tableData").count() > 0:
            logger.warning("⚠️ 当前在用户详情页面，不执行分页导航")
            return False

        # 🆕 获取分页前的页面状态
        before_page_info = await get_current_page_info(page)
        logger.info(f"📄 分页前状态: {before_page_info}")

        # 检查是否存在下一页按钮
        next_button_exists = await page.locator(".pager-btm .next02 a, .pager-btm .next a").count() > 0
        if not next_button_exists:
            logger.info("📄 没有发现下一页按钮，已到最后一页")
            return False

        # 使用智能选择器点击下一页
        logger.info("📄 点击下一页按钮...")
        click_success = await selector_executor.smart_click("kaipoke", "performance_report", "next_page_button")

        if not click_success:
            logger.warning("❌ 下一页按钮点击失败")
            return False

        # 🆕 等待页面更新并验证
        logger.info("📄 等待页面更新...")
        await page.wait_for_timeout(3000)  # 等待页面加载

        # 🆕 多重验证机制
        verification_attempts = 3
        for attempt in range(verification_attempts):
            logger.info(f"📄 验证分页结果 (尝试 {attempt + 1}/{verification_attempts})...")

            # 获取分页后的页面状态
            after_page_info = await get_current_page_info(page)
            logger.info(f"📄 分页后状态: {after_page_info}")

            # 验证1: URL变化
            url_changed = after_page_info['url'] != before_page_info['url']

            # 验证2: 分页文本变化
            pager_changed = after_page_info['pager_text'] != before_page_info['pager_text']

            # 验证3: 第一个用户变化（最可靠的指标）
            first_user_changed = (after_page_info['first_user'] != before_page_info['first_user'] and
                                after_page_info['first_user'] != "")

            # 验证4: URL页码变化
            url_page_changed = (after_page_info['url_page'] is not None and
                              before_page_info['url_page'] is not None and
                              after_page_info['url_page'] > before_page_info['url_page'])

            # 记录验证结果
            logger.info(f"📊 验证结果:")
            logger.info(f"   URL变化: {url_changed}")
            logger.info(f"   分页文本变化: {pager_changed}")
            logger.info(f"   第一个用户变化: {first_user_changed}")
            logger.info(f"   URL页码变化: {url_page_changed}")

            # 🆕 严格的成功判断：至少两个指标变化
            success_indicators = sum([url_changed, pager_changed, first_user_changed, url_page_changed])

            if success_indicators >= 2:
                logger.info(f"✅ 分页成功验证通过 ({success_indicators}/4 个指标变化)")
                return True
            elif success_indicators == 1:
                logger.warning(f"⚠️ 分页可能成功但验证不充分 ({success_indicators}/4 个指标变化)")
                if attempt < verification_attempts - 1:
                    logger.info("📄 等待更长时间后重新验证...")
                    await page.wait_for_timeout(2000)
                    continue
            else:
                logger.warning(f"⚠️ 分页验证失败 ({success_indicators}/4 个指标变化)")
                if attempt < verification_attempts - 1:
                    logger.info("📄 等待后重新验证...")
                    await page.wait_for_timeout(2000)
                    continue

            break

        # 🆕 最终判断 - 分页失败时进行诊断
        logger.error("❌ 分页验证最终失败 - 页面内容未发生预期变化")
        logger.error("❌ 这表明分页操作实际上没有成功")

        # 🆕 诊断分页失败原因
        await diagnose_pagination_failure(page, before_page_info, after_page_info)

        return False

    except Exception as e:
        logger.error(f"导航到下一页时发生错误: {e}")
        return False
