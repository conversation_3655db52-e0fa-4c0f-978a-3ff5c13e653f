"""
Kaipoke Daily Performance Report Workflow (MCP強化版)
基于RPA代码的核心流程，复用kaipoke_performance_report的成熟组件

核心流程：
1. 日期循环处理：遍历前月的每一天
2. 导航到提供日別実績登録页面
3. 对每个日期进行数据抽取和分页处理
4. 批量写入Google Sheets

🆕 优化内容：
- 高效批量写入：参考kaipoke_performance_report，使用append_values方法，500行/批
- 利用者姓名提取：确保数据包含用户姓名字段
- 增强据点导航：多重策略确保据点选择成功
- 施設区分说明：指介护服务类型分类（如通所介護、訪問介護、障害者総合支援等）
"""

import asyncio
import re
from datetime import datetime, timedelta
from logger_config import logger
from core.browser.browser_manager import BrowserManager
from core.selector_executor import SelectorExecutor
from core.rpa_tools.kaipoke_login_service import kaipoke_login_with_env
from core.gsuite.sheets_client import SheetsClient
from core.rpa_tools.data_processor import DataProcessor


async def async_run(config: dict):
    """
    Kaipoke Daily Performance Report工作流主入口
    复用kaipoke_performance_report的架构，专门化实现日报表逻辑
    """
    logger.info("🚀 开始执行Kaipoke Daily Performance Report工作流（MCP強化版）")
    
    # 从配置获取基本信息
    common_config = config.get('config', {})
    tasks = config.get('tasks', [])
    
    if not tasks:
        logger.error("❌ 配置文件中没有定义任务")
        return
    
    # 初始化Google Sheets客户端
    spreadsheet_id = common_config.get('spreadsheet_id')
    if not spreadsheet_id:
        logger.error("❌ 配置文件中缺少spreadsheet_id")
        return

    sheets_client = SheetsClient()
    sheets_client.spreadsheet_id = spreadsheet_id
    browser_manager = BrowserManager()
    
    try:
        # 一次性浏览器启动和登录
        await browser_manager.start_browser(headless=False)
        page = await browser_manager.get_page()
        selector_executor = SelectorExecutor(page)
        
        # 🆕 MCP备份工具初始化
        await selector_executor.initialize_mcp_fallback()
        
        # 使用环境变量登录Kaipoke
        login_url = common_config.get('login_url')
        corp_id_env = common_config.get('corporation_id_env', 'KAIPOKE_CORPORATION_ID')
        login_id_env = common_config.get('login_id_env', 'KAIPOKE_MEMBER_LOGIN_ID')
        password_env = common_config.get('password_env', 'KAIPOKE_PASSWORD')
        
        login_success = await kaipoke_login_with_env(
            page, corp_id_env, login_id_env, password_env, login_url
        )
        
        if not login_success:
            logger.error("❌ Kaipoke登录失败，工作流中断")
            return
        
        logger.info("✅ Kaipoke登录成功，开始处理各据点任务")
        
        # 处理各据点任务
        for task_config in tasks:
            await execute_daily_performance_task(
                task_config, common_config, sheets_client, selector_executor
            )
        
        logger.info("✅ Kaipoke Daily Performance Report工作流完成")
        
    except Exception as e:
        logger.error(f"❌ 工作流执行失败: {e}", exc_info=True)
    finally:
        await browser_manager.close()


async def execute_daily_performance_task(task_config: dict, common_config: dict, 
                                       sheets_client: SheetsClient, selector_executor: SelectorExecutor):
    """
    执行单个据点的日报表任务
    基于RPA代码的核心流程
    """
    task_id = task_config.get('task_id')
    facility_name = task_config.get('facility_name')
    element_text = task_config.get('element_text')
    target_sheet_name = task_config.get('target_sheet_name')
    target_range = task_config.get('target_range')
    
    logger.info(f"🏢 开始处理据点: {facility_name} ({task_id})")
    
    page = selector_executor.page
    
    try:
        # 1. 导航到据点选择页面
        await navigate_to_facility_selection(selector_executor)
        
        # 2. 选择目标据点
        await select_target_facility(selector_executor, element_text, facility_name)
        
        # 3. 导航到提供日別実績登録页面
        await navigate_to_daily_performance_page(selector_executor)
        
        # 4. 生成前月日期列表
        date_list = generate_previous_month_dates()
        logger.info(f"📅 生成前月日期列表: {len(date_list)} 天")
        
        # 5. 🆕 安全清空目标Sheet，确保每月数据完整替换
        await safe_clear_monthly_data(sheets_client, target_range, facility_name)

        logger.info(f"📊 开始处理目标Sheet: {target_range}")
        
        # 6. 🆕 智能数据处理：根据数据量选择缓存或流式写入策略
        estimated_total_rows = len(date_list) * 100  # 估算每天100行数据

        if estimated_total_rows > 10000:
            # 超大数据集：使用流式写入，避免内存溢出
            logger.info(f"📊 预估数据量 {estimated_total_rows} 行，使用流式写入策略")

            # 🆕 先写入表头
            header_row = create_daily_performance_header()
            await write_daily_data_to_sheets(sheets_client, target_range, [header_row])
            logger.info(f"📋 表头已写入: {header_row}")

            await process_dates_with_streaming_write(
                selector_executor, date_list, facility_name, sheets_client, target_range
            )
        else:
            # 中小数据集：使用缓存批量写入
            logger.info(f"📊 预估数据量 {estimated_total_rows} 行，使用缓存批量写入策略")

            # 🆕 创建表头并添加到数据开头
            header_row = create_daily_performance_header()
            all_data = [header_row]  # 从表头开始
            logger.info(f"📋 表头已添加: {header_row}")

            for date_info in date_list:
                daily_data = await process_single_date_data(
                    selector_executor, date_info, facility_name
                )
                if daily_data:
                    all_data.extend(daily_data)
                    logger.info(f"✅ 日期 {date_info['formatted']} 数据缓存完成: {len(daily_data)} 行")
                else:
                    logger.warning(f"⚠️ 日期 {date_info['formatted']} 没有数据")

            # 8. 批量写入所有缓存的数据
            if all_data and len(all_data) > 1:  # 确保除了表头还有数据
                await write_daily_data_to_sheets(sheets_client, target_range, all_data)

                # 🆕 数据完整性验证和摘要
                summary = get_monthly_data_summary(all_data)
                logger.info(f"📊 数据摘要 - 总行数: {summary['total_rows']}, 表头: {summary['header_rows']}, 日期行: {summary['date_rows']}, 数据行: {summary['data_rows']}, 覆盖天数: {summary['days_covered']}")
                logger.info(f"✅ 据点 {facility_name} 处理完成，共写入 {len(all_data)} 行数据 (含表头)")
            elif len(all_data) == 1:
                # 只有表头，没有实际数据
                await write_daily_data_to_sheets(sheets_client, target_range, all_data)
                logger.warning(f"⚠️ 据点 {facility_name} 只有表头，没有实际数据")
            else:
                logger.warning(f"⚠️ 据点 {facility_name} 没有数据")
        
        # 8. 触发GAS格式化（如果配置了）
        gas_config = task_config.get('gas_trigger')
        if gas_config:
            await trigger_gas_formatting(gas_config)

        # 9. 处理完成后，导航回据点选择页面（为下一个据点做准备）
        await navigate_back_to_facility_selection(selector_executor)

    except Exception as e:
        logger.error(f"❌ 据点 {facility_name} 处理失败: {e}", exc_info=True)
        # 即使出错也尝试回到据点选择页面
        try:
            await navigate_back_to_facility_selection(selector_executor)
        except:
            pass


async def navigate_to_facility_selection(selector_executor: SelectorExecutor):
    """
    导航到据点选择页面
    🆕 增强导航逻辑，参考kaipoke_performance_report的成功模式
    """
    page = selector_executor.page

    try:
        # 🆕 增强页面状态检测，参考kaipoke_performance_report的逻辑
        current_url = page.url
        logger.debug(f"📄 当前页面URL: {current_url}")

        if "COM020101.do" in current_url:
            logger.info("✅ 已在据点选择页面，跳过レセプト菜单点击")
            return
        elif "MEM087" in current_url or "plan_actual" in current_url:
            # 如果在用户详情页面或实绩管理页面，需要先返回据点选择页面
            logger.info("🔄 检测到在用户详情页面，先返回据点选择页面...")
            try:
                await page.goto("https://r.kaipoke.biz/kaipokebiz/common/COM020101.do?fromBP=true",
                               wait_until='networkidle', timeout=30000)
                await page.wait_for_timeout(2000)
                logger.info("✅ 成功返回据点选择页面")
                return
            except Exception as e:
                logger.warning(f"⚠️ 返回据点选择页面失败: {e}")
                # 继续尝试正常流程

        # 点击レセプト菜单 - 使用增强的多重策略
        logger.info("📋 レセプトメニューをクリック")

        # 🆕 多重点击策略，参考kaipoke_performance_report的成功模式
        click_success = False
        click_strategies = [
            ("智能选择器", lambda: selector_executor.smart_click("kaipoke", "daily_performance_report", "receipt_menu")),
            ("直接选择器", lambda: page.click(".mainCtg li:nth-of-type(1) a", timeout=10000)),
            ("备用选择器", lambda: page.click(".mainCtg li:first-child a", timeout=10000)),
            ("JavaScript点击", lambda: page.evaluate("""
                () => {
                    const link = document.querySelector('.mainCtg li:nth-of-type(1) a');
                    if (link) {
                        link.click();
                        return true;
                    }
                    return false;
                }
            """))
        ]

        for strategy_name, strategy_func in click_strategies:
            try:
                logger.info(f"🔄 尝试 {strategy_name}")
                result = await strategy_func()
                if result:
                    click_success = True
                    logger.info(f"✅ {strategy_name} 成功")
                    break
            except Exception as e:
                logger.warning(f"⚠️ {strategy_name} 失败: {e}")
                continue

        if not click_success:
            logger.error("❌ 所有レセプト菜单点击策略都失败")
            raise Exception("导航到据点选择页面失败")

        await page.wait_for_timeout(2000)
        logger.info("✅ 成功导航到据点选择页面")

    except Exception as e:
        logger.error(f"❌ 导航到据点选择页面失败: {e}")
        raise


async def navigate_back_to_facility_selection(selector_executor: SelectorExecutor):
    """
    从当前页面导航回据点选择页面
    基于kaipoke_performance_report的逻辑
    """
    page = selector_executor.page

    try:
        # 方法1：直接访问据点选择页面URL
        facility_selection_url = "https://r.kaipoke.biz/kaipokebiz/common/COM020101.do?fromBP=true"
        logger.info(f"🔄 导航回据点选择页面: {facility_selection_url}")
        await page.goto(facility_selection_url)
        await page.wait_for_timeout(3000)

        # 验证是否成功到达据点选择页面
        current_url = page.url
        if "COM020101.do" in current_url:
            logger.info("✅ 成功返回据点选择页面")
        else:
            logger.warning(f"⚠️ 可能未正确返回据点选择页面，当前URL: {current_url}")

    except Exception as e:
        logger.error(f"❌ 返回据点选择页面失败: {e}")
        # 备用方法：尝试点击レセプト菜单
        try:
            logger.info("🔄 尝试备用方法：点击レセプト菜单")
            if await selector_executor.smart_click("kaipoke", "daily_performance_report", "receipt_menu"):
                await page.wait_for_timeout(2000)
                logger.info("✅ 备用方法成功")
            else:
                logger.warning("⚠️ 备用方法也失败")
        except Exception as backup_e:
            logger.error(f"❌ 备用方法失败: {backup_e}")


async def select_target_facility(selector_executor: SelectorExecutor, element_text: str, facility_name: str):
    """选择目标据点"""
    page = selector_executor.page
    
    # 使用XPath选择器查找据点
    facility_xpath = f'//a[contains(text(), "{element_text}")]'
    
    try:
        await page.wait_for_selector(f'xpath={facility_xpath}', timeout=10000)
        await page.click(f'xpath={facility_xpath}')
        await page.wait_for_timeout(2000)
        logger.info(f"✅ 成功选择据点: {facility_name} ({element_text})")
    except Exception as e:
        logger.error(f"❌ 据点选择失败: {e}")
        raise Exception(f"选择据点失败: {facility_name}")


async def navigate_to_daily_performance_page(selector_executor: SelectorExecutor):
    """
    导航到提供日別実績登録页面
    🆕 优化悬停逻辑，保持鼠标悬停状态并立即点击
    """
    page = selector_executor.page

    # 🆕 优化策略：悬停后立即点击，避免子菜单消失
    success = False

    # 方法1：悬停后立即点击（不等待太久）
    logger.info("🔄 方法1：悬停后立即点击")
    if await selector_executor.smart_hover("kaipoke", "daily_performance_report", "schedule_performance_hover"):
        # 短暂等待子菜单显示，但不要太久
        await page.wait_for_timeout(1000)
        logger.info("⏳ 等待子菜单显示（1秒）")

        if await selector_executor.smart_click("kaipoke", "daily_performance_report", "daily_performance_registration"):
            success = True
            logger.info("✅ 方法1成功：悬停后立即点击")

    # 方法2：保持悬停状态的连续操作
    if not success:
        logger.info("🔄 方法2：保持悬停状态的连续操作")
        try:
            # 获取悬停元素并保持悬停
            hover_element = await page.locator("li:nth-of-type(4):nth-child(7) img").first
            if await hover_element.count() > 0:
                await hover_element.hover()
                await page.wait_for_timeout(500)  # 更短的等待时间

                # 立即点击子菜单项
                click_element = await page.locator("li:nth-of-type(4):nth-child(7) li:nth-of-type(2) a").first
                if await click_element.count() > 0:
                    await click_element.click()
                    success = True
                    logger.info("✅ 方法2成功：保持悬停状态点击")
        except Exception as e:
            logger.warning(f"⚠️ 方法2失败: {e}")

    # 方法3：JavaScript强制点击（备用方案）
    if not success:
        logger.info("🔄 方法3：JavaScript强制点击")
        try:
            # 使用JavaScript同时处理悬停和点击
            js_code = """
            () => {
                const hoverElement = document.querySelector('li:nth-of-type(4):nth-child(7) img');
                const clickElement = document.querySelector('li:nth-of-type(4):nth-child(7) li:nth-of-type(2) a');

                if (hoverElement && clickElement) {
                    // 触发悬停事件
                    hoverElement.dispatchEvent(new MouseEvent('mouseover', {bubbles: true}));
                    // 短暂延迟后点击
                    setTimeout(() => {
                        clickElement.click();
                    }, 200);
                    return true;
                }
                return false;
            }
            """
            result = await page.evaluate(js_code)
            if result:
                success = True
                logger.info("✅ 方法3成功：JavaScript强制点击")
                await page.wait_for_timeout(1000)  # 等待页面响应
        except Exception as e:
            logger.warning(f"⚠️ 方法3失败: {e}")

    if not success:
        logger.error("❌ 所有方法都失败，提供日別実績登録点击失败")
        raise Exception("导航到日报表页面失败")

    await page.wait_for_timeout(3000)
    logger.info("✅ 成功导航到提供日別実績登録页面")


def generate_previous_month_dates():
    """
    生成前月的日期列表
    基于RPA代码的日期生成逻辑，使用令和年格式
    """
    today = datetime.now()
    # 获取前月的第一天
    first_day_current_month = today.replace(day=1)
    last_day_previous_month = first_day_current_month - timedelta(days=1)
    first_day_previous_month = last_day_previous_month.replace(day=1)

    # 计算令和年
    reiwa_year = last_day_previous_month.year - 2018  # 2019年是令和元年
    month_year_reiwa = f"令和{reiwa_year}年{last_day_previous_month.month}月"

    # 生成前月的所有日期
    date_list = []
    current_date = first_day_previous_month

    while current_date <= last_day_previous_month:
        # 格式化为日本格式：例如 "1日（月）"
        day_str = current_date.strftime("%d").lstrip('0') + "日"
        weekday_map = {
            0: "月", 1: "火", 2: "水", 3: "木", 4: "金", 5: "土", 6: "日"
        }
        weekday_str = weekday_map[current_date.weekday()]
        formatted_date = f"{day_str}（{weekday_str}）"

        # 生成完整的令和年月日格式（用于GAS处理）
        full_reiwa_date = f"令和{reiwa_year}年{last_day_previous_month.month}月{current_date.day}日（{weekday_str}）"

        date_list.append({
            'formatted': formatted_date,
            'date_obj': current_date,
            'month_year_reiwa': month_year_reiwa,
            'full_reiwa_date': full_reiwa_date
        })

        current_date += timedelta(days=1)

    return date_list


async def process_single_date_data(selector_executor: SelectorExecutor, date_info: dict, facility_name: str, include_header: bool = False):
    """
    处理单个日期的数据
    基于RPA代码的核心逻辑，修正为正确的数据格式
    """
    page = selector_executor.page
    formatted_date = date_info['formatted']
    month_year_reiwa = date_info['month_year_reiwa']
    full_reiwa_date = date_info['full_reiwa_date']

    logger.info(f"📅 处理日期: {formatted_date} ({month_year_reiwa})")

    try:
        # 1. 选择月份（使用令和年格式）
        if not await selector_executor.smart_select_option(
            "kaipoke", "daily_performance_report", "service_offer_month_select",
            text=month_year_reiwa
        ):
            logger.warning(f"⚠️ 月份选择失败: {month_year_reiwa}")
            return []

        # 2. 点击页面标题确认月份
        await selector_executor.smart_click("kaipoke", "daily_performance_report", "page_title_confirm")
        await page.wait_for_timeout(1000)

        # 3. 选择日期
        if not await selector_executor.smart_select_option(
            "kaipoke", "daily_performance_report", "service_offer_day_select",
            text=formatted_date
        ):
            logger.warning(f"⚠️ 日期选择失败: {formatted_date}")
            return []

        # 4. 点击页面标题确认日期
        await selector_executor.smart_click("kaipoke", "daily_performance_report", "page_title_confirm")
        await page.wait_for_timeout(2000)

        # 5. 点击筛选条件：実績が「1」のみ表示
        await selector_executor.smart_click("kaipoke", "daily_performance_report", "achievement_filter_checkbox")
        await page.wait_for_timeout(1500)

        # 6. 获取总记录数
        total_records = await get_total_records_count(selector_executor)

        # 7. 抽取表格数据（包含分页处理）
        table_data = await extract_daily_table_data_with_pagination(selector_executor)

        # 🆕 构建返回数据（按照Excel表格格式：日期行在A列，数据行正常显示）
        result_data = []

        if table_data:
            # 🆕 日期行：A列显示完整日期，其他列为空，J列显示总记录数
            date_row = [full_reiwa_date, "", "", "", "", "", "", "", "", str(total_records)]
            result_data.append(date_row)

            # 🆕 数据行：A列显示実際数据内容（選択、利用者名等），J列为空
            for i, row in enumerate(table_data):
                # 确保数据行有正确的列数（9列数据）
                if len(row) >= 9:
                    # A-I列为表格数据，J列为空（总记录数只在日期行显示）
                    data_row = row[:9] + [""]
                else:
                    # 如果数据不足9列，补齐到10列
                    data_row = (row + [""] * 9)[:9] + [""]

                result_data.append(data_row)

        logger.info(f"✅ 日期 {formatted_date} 处理完成: {len(table_data)} 行用户数据，总记录数: {total_records}")
        return result_data

    except Exception as e:
        logger.error(f"❌ 日期 {formatted_date} 处理失败: {e}")
        return []


async def get_total_records_count(selector_executor: SelectorExecutor) -> int:
    """获取总记录数"""
    try:
        # 使用选择器获取总记录数文本
        total_text = await selector_executor.smart_get_text("kaipoke", "daily_performance_report", "total_records_display")
        if total_text:
            # 提取数字
            import re
            numbers = re.findall(r'\d+', total_text)
            if numbers:
                return int(numbers[0])
        return 0
    except Exception as e:
        logger.warning(f"⚠️ 获取总记录数失败: {e}")
        return 0


async def extract_daily_table_data_with_pagination(selector_executor: SelectorExecutor) -> list:
    """
    抽取日报表数据（包含分页处理）
    基于RPA代码的分页逻辑，使用.next02 a进行翻页
    """
    page = selector_executor.page
    all_data = []
    current_page = 1

    try:
        # 🆕 处理第一页数据（不包含表头，因为已在主函数中添加）
        first_page_data = await extract_current_page_data(selector_executor, include_headers=False)
        if first_page_data:
            all_data.extend(first_page_data)
            logger.info(f"📊 第1页数据: {len(first_page_data)} 行")

        # 持续翻页直到没有下一页
        while True:
            try:
                # 检查是否存在下一页按钮
                next_button_exists = await page.locator(".next02 a").count() > 0
                if not next_button_exists:
                    logger.info(f"📄 已到达最后一页，总共处理 {current_page} 页")
                    break

                # 点击下一页按钮
                logger.info(f"🔄 点击下一页按钮 (.next02 a)")
                await page.click(".next02 a")
                await page.wait_for_timeout(1500)  # 等待页面加载

                current_page += 1

                # 验证页面是否真的跳转了（基于RPA代码的验证逻辑）
                page_changed = await verify_page_navigation(selector_executor, current_page)
                if not page_changed:
                    logger.warning(f"⚠️ 第{current_page}页导航失败，可能已到最后一页")
                    break

                # 🆕 抽取当前页数据（不包含表头）
                page_data = await extract_current_page_data(selector_executor, include_headers=False)
                if page_data:
                    all_data.extend(page_data)
                    logger.info(f"📊 第{current_page}页数据: {len(page_data)} 行")
                else:
                    logger.warning(f"⚠️ 第{current_page}页没有数据")
                    break

            except Exception as e:
                logger.error(f"❌ 第{current_page}页处理失败: {e}")
                break

        logger.info(f"✅ 分页数据抽取完成: 总共 {len(all_data)} 行，处理了 {current_page} 页")
        return all_data

    except Exception as e:
        logger.error(f"❌ 分页数据抽取失败: {e}")
        return all_data


async def verify_page_navigation(selector_executor: SelectorExecutor, target_page: int) -> bool:
    """
    验证页面是否成功跳转到目标页
    基于RPA代码的验证逻辑：最多尝试20次，每次1.5秒
    """
    page = selector_executor.page

    for attempt in range(20):
        try:
            page_info = await selector_executor.smart_get_text("kaipoke", "daily_performance_report", "page_info_display")
            if page_info:
                # 提取当前页码，例如 "2/ 5ページ" -> 2
                import re
                match = re.search(r'^(\d+)', page_info)
                if match and int(match.group(1)) == target_page:
                    logger.info(f"✅ 成功跳转到第{target_page}页")
                    return True

            await page.wait_for_timeout(1500)

        except Exception:
            await page.wait_for_timeout(1500)
            continue

    logger.warning(f"⚠️ 页面跳转验证超时: 目标第{target_page}页")
    return False


async def get_total_pages_count(selector_executor: SelectorExecutor) -> int:
    """获取总页数"""
    try:
        page_info = await selector_executor.smart_get_text("kaipoke", "daily_performance_report", "page_info_display")
        if page_info:
            # 解析页面信息，例如 "1/ 5ページ" -> 5
            import re
            match = re.search(r'/\s*(\d+)', page_info)
            if match:
                total_pages = int(match.group(1))
                logger.info(f"📄 解析总页数: {total_pages} (原文: {page_info})")
                return total_pages
        return 1
    except Exception as e:
        logger.warning(f"⚠️ 获取总页数失败: {e}")
        return 1


async def extract_current_page_data(selector_executor: SelectorExecutor, include_headers: bool = False) -> list:
    """
    抽取当前页面的完整表格数据
    🆕 获取包括表头在内的完整表格信息，增强错误处理

    Args:
        selector_executor: 选择器执行器
        include_headers: 是否包含表头（第一页时为True）
    """
    page = selector_executor.page

    try:
        # 🆕 增强页面状态检查
        if page.is_closed():
            logger.error("❌ 页面已关闭，无法提取数据")
            return []

        # 等待表格加载
        await page.wait_for_selector("#tblMem094501", timeout=10000)

        data = []

        # 🆕 如果需要包含表头，先获取表头信息
        if include_headers:
            try:
                # 获取表头（通常在thead或第一行）
                header_rows = await page.locator("#tblMem094501 > thead > tr").all()
                if not header_rows:
                    # 如果没有thead，尝试获取第一行作为表头
                    header_rows = await page.locator("#tblMem094501 > tbody > tr").first.all()

                for header_row in header_rows:
                    header_cells = await header_row.locator("th, td").all()
                    header_data = []
                    for cell in header_cells:
                        cell_text = await cell.text_content()
                        header_data.append(cell_text.strip() if cell_text else "")

                    if header_data and not all(cell == "" for cell in header_data):
                        data.append(header_data)
                        logger.debug(f"📋 表头行: {header_data}")

            except Exception as header_error:
                logger.warning(f"⚠️ 表头获取失败: {header_error}")

        # 获取表体数据
        rows = await page.locator("#tblMem094501 > tbody > tr").all()

        for i, row in enumerate(rows):
            try:
                # 🆕 增强单行数据提取的错误处理
                cells = await row.locator("td").all()
                row_data = []

                for j, cell in enumerate(cells):
                    try:
                        cell_text = await cell.text_content()
                        row_data.append(cell_text.strip() if cell_text else "")
                    except Exception as cell_error:
                        logger.warning(f"⚠️ 第{i+1}行第{j+1}列数据提取失败: {cell_error}")
                        row_data.append("")

                # 🆕 增强数据处理：确保包含利用者姓名
                if row_data and not all(cell == "" for cell in row_data):
                    # 根据RPA代码，利用者姓名通常在第二列（索引1）
                    if len(row_data) > 1:
                        user_name = row_data[1] if row_data[1] else "不明"
                        # 清理用户名，移除不需要的后缀
                        user_name = clean_user_name(user_name)
                        row_data[1] = user_name

                    data.append(row_data)

            except Exception as row_error:
                logger.warning(f"⚠️ 第{i+1}行数据提取失败: {row_error}")
                continue

        # 🆕 不再自动移除表头，保留完整表格结构
        logger.debug(f"📊 当前页面抽取数据: {len(data)} 行（包含表头: {include_headers}）")
        return data

    except Exception as e:
        logger.error(f"❌ 当前页面数据抽取失败: {e}")
        # 🆕 检查是否是页面关闭导致的错误
        if "Target page, context or browser has been closed" in str(e):
            logger.error("❌ 浏览器或页面已关闭，停止数据提取")
        return []


def clean_user_name(raw_name: str) -> str:
    """
    清理用户名，移除不需要的后缀
    参考kaipoke_performance_report的用户名清理逻辑
    """
    if not raw_name:
        return "不明"

    # 移除常见的后缀
    suffixes_to_remove = [
        "さん", "様", "氏", "殿",
        "（", ")", "(", "）",
        "　", "  "  # 全角和半角空格
    ]

    cleaned_name = raw_name.strip()
    for suffix in suffixes_to_remove:
        if suffix in cleaned_name:
            cleaned_name = cleaned_name.split(suffix)[0].strip()

    return cleaned_name if cleaned_name else "不明"


async def click_next_page_with_verification(selector_executor: SelectorExecutor, target_page: int) -> bool:
    """
    点击下一页并验证页面切换
    基于RPA代码的验证逻辑
    """
    page = selector_executor.page

    try:
        # 点击下一页按钮
        if not await selector_executor.smart_click("kaipoke", "daily_performance_report", "next_page_button"):
            return False

        # 等待页面切换
        await page.wait_for_timeout(1500)

        # 验证页面切换（最多尝试20次，每次1.5秒）
        for attempt in range(20):
            try:
                page_info = await selector_executor.smart_get_text("kaipoke", "daily_performance_report", "page_info_display")
                if page_info:
                    # 提取当前页码
                    import re
                    match = re.search(r'^(\d+)', page_info)
                    if match and int(match.group(1)) == target_page:
                        logger.info(f"✅ 成功切换到第{target_page}页")
                        return True

                await page.wait_for_timeout(1500)

            except Exception:
                await page.wait_for_timeout(1500)
                continue

        logger.warning(f"⚠️ 页面切换验证超时: 目标第{target_page}页")
        return False

    except Exception as e:
        logger.error(f"❌ 下一页点击失败: {e}")
        return False


async def write_daily_data_to_sheets(sheets_client: SheetsClient, target_range: str, data: list):
    """
    优雅的批量写入日报表数据到Google Sheets
    🆕 架构师级别的批量写入优化：
    - 智能批次大小调整
    - 指数退避重试机制
    - 统一错误处理
    - 内存优化和超时防护
    """
    try:
        if not data:
            logger.warning("⚠️ 没有数据需要写入")
            return

        logger.info(f"📊 开始优雅批量写入 {len(data)} 行数据到 {target_range}")

        # 🆕 使用优雅的批量写入策略
        success = await execute_graceful_batch_write(sheets_client, target_range, data)

        if success:
            logger.info(f"✅ 优雅批量写入完成: {len(data)} 行数据")
        else:
            logger.error(f"❌ 批量写入失败，请检查网络连接和API配额")
            raise Exception("批量写入失败")

    except Exception as e:
        logger.error(f"❌ 数据写入异常: {e}")
        raise


async def execute_graceful_batch_write(sheets_client: SheetsClient, target_range: str, data: list) -> bool:
    """
    执行优雅的批量写入策略

    核心优化：
    1. 智能批次大小：根据数据量和历史成功率动态调整
    2. 指数退避重试：1s -> 2s -> 4s -> 8s
    3. 渐进式降级：大批次失败时自动降级到小批次
    4. 内存友好：避免大数据集长时间驻留
    """

    # 🆕 智能批次大小策略（基于数据量和API稳定性）
    batch_strategies = [
        {"size": 1000, "sleep": 0.5, "name": "超大批次"},
        {"size": 500, "sleep": 1.0, "name": "大批次"},
        {"size": 200, "sleep": 1.5, "name": "中批次"},
        {"size": 50, "sleep": 2.0, "name": "小批次"},
        {"size": 10, "sleep": 3.0, "name": "微批次"}
    ]

    # 根据数据量选择起始策略
    if len(data) > 8000:
        start_strategy = 2  # 从中批次开始
    elif len(data) > 3000:
        start_strategy = 1  # 从大批次开始
    else:
        start_strategy = 0  # 从超大批次开始

    sheet_name = target_range.split('!')[0]

    # 尝试不同的批次策略
    for strategy_idx in range(start_strategy, len(batch_strategies)):
        strategy = batch_strategies[strategy_idx]
        batch_size = strategy["size"]
        sleep_time = strategy["sleep"]
        strategy_name = strategy["name"]

        logger.info(f"🔄 尝试 {strategy_name} 策略: {batch_size} 行/批次")

        try:
            success = await write_with_strategy(
                sheets_client, sheet_name, data, batch_size, sleep_time
            )

            if success:
                logger.info(f"✅ {strategy_name} 策略成功完成写入")
                return True
            else:
                logger.warning(f"⚠️ {strategy_name} 策略失败，尝试下一个策略")

        except Exception as e:
            logger.warning(f"⚠️ {strategy_name} 策略异常: {e}")
            continue

    logger.error("❌ 所有批次策略都失败")
    return False


async def write_with_strategy(sheets_client: SheetsClient, sheet_name: str,
                            data: list, batch_size: int, sleep_time: float) -> bool:
    """
    使用指定策略执行写入，包含重试机制
    """
    total_batches = (len(data) + batch_size - 1) // batch_size
    max_retries = 3

    for i in range(0, len(data), batch_size):
        batch_data = data[i:i + batch_size]
        batch_num = i // batch_size + 1

        # 对每个批次进行重试
        success = False
        for retry in range(max_retries + 1):
            try:
                start_row = i + 1
                end_row = start_row + len(batch_data) - 1
                batch_range = f"{sheet_name}!A{start_row}:J{end_row}"

                # 🆕 使用更稳定的写入方法
                result = sheets_client.service.spreadsheets().values().update(
                    spreadsheetId=sheets_client.spreadsheet_id,
                    range=batch_range,
                    valueInputOption='USER_ENTERED',
                    body={'values': batch_data}
                ).execute()

                updated_cells = result.get('updatedCells', 0)
                logger.info(f"✅ 批次 {batch_num}/{total_batches} 写入成功: {len(batch_data)} 行，{updated_cells} 个单元格")

                success = True
                break

            except Exception as e:
                wait_time = min(2 ** retry, 16)  # 指数退避，最大16秒
                logger.warning(f"⚠️ 批次 {batch_num} 重试 {retry + 1}/{max_retries + 1} 失败: {e}")

                if retry < max_retries:
                    logger.info(f"🔄 等待 {wait_time}s 后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"❌ 批次 {batch_num} 达到最大重试次数")
                    return False

        if not success:
            return False

        # 🆕 智能等待：成功批次之间的适度等待
        if batch_num < total_batches:
            await asyncio.sleep(sleep_time)

    return True


async def trigger_gas_formatting(gas_config: dict):
    """
    触发GAS格式化
    🆕 增强调试：详细日志记录，帮助诊断GAS触发问题
    """
    try:
        import aiohttp
        import json

        url = gas_config.get('url')
        password = gas_config.get('password')

        if not url or not password:
            logger.warning("⚠️ GAS配置不完整，跳过格式化")
            logger.warning(f"   URL: {url}")
            logger.warning(f"   Password: {password}")
            return

        # 🆕 构建JSON格式数据，匹配GAS的doPost函数期望
        json_data = {'password': password}

        logger.info(f"🔄 开始触发GAS格式化")
        logger.info(f"   URL: {url}")
        logger.info(f"   Password: {password}")
        logger.info(f"   JSON Data: {json_data}")

        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60)) as session:
            try:
                async with session.post(
                    url,
                    json=json_data,  # 🆕 使用JSON数据，匹配doPost中的JSON.parse
                    headers={'Content-Type': 'application/json'}
                ) as response:
                    response_text = await response.text()
                    response_headers = dict(response.headers)

                    logger.info(f"📡 GAS响应状态: {response.status}")
                    logger.info(f"📡 GAS响应头: {response_headers}")
                    logger.info(f"📡 GAS响应内容长度: {len(response_text)}")
                    logger.info(f"📡 GAS响应内容预览: {response_text[:500]}...")

                    if response.status == 200:
                        # 🆕 更详细的成功检查
                        if "スクリプトが完了しましたが、何も返されませんでした" in response_text:
                            logger.info(f"✅ GAS格式化触发成功: doPost函数执行完成（{password}）")
                        elif "<!DOCTYPE html>" in response_text and "エラー" not in response_text:
                            # 这是GAS的标准"无返回值"页面，实际上表示成功
                            logger.info(f"✅ GAS格式化触发成功: formatData函数执行完成（{password}）")
                        elif "抽出結果" in response_text:
                            # 检查是否包含预期的处理结果
                            logger.info(f"✅ GAS格式化触发成功: 检测到抽出結果关键词（{password}）")
                        else:
                            logger.info(f"✅ GAS格式化触发成功: {response_text[:200]}...")
                    elif response.status == 302:
                        # 重定向通常也表示成功
                        redirect_location = response_headers.get('location', 'Unknown')
                        logger.info(f"✅ GAS格式化触发成功: 重定向到 {redirect_location}")
                    else:
                        logger.warning(f"⚠️ GAS格式化触发失败: {response.status}")
                        logger.warning(f"   响应内容: {response_text[:500]}...")

            except aiohttp.ClientError as client_error:
                logger.error(f"❌ GAS HTTP请求失败: {client_error}")
            except asyncio.TimeoutError:
                logger.error(f"❌ GAS请求超时（60秒）")

    except Exception as e:
        logger.error(f"❌ GAS格式化触发异常: {e}")
        import traceback
        logger.error(f"   详细错误: {traceback.format_exc()}")


async def safe_clear_monthly_data(sheets_client: SheetsClient, target_range: str, facility_name: str):
    """
    安全清空月度数据，确保数据完整性

    策略：
    1. 备份当前数据（可选）
    2. 清空指定范围
    3. 验证清空结果
    4. 记录操作日志
    """
    try:
        logger.info(f"🧹 开始安全清空 {facility_name} 的月度数据: {target_range}")

        # 1. 获取当前数据行数（用于日志记录）
        try:
            sheet_name = target_range.split('!')[0]
            current_data = sheets_client.read_sheet(sheets_client.spreadsheet_id, f"{sheet_name}!A:J")
            current_rows = len(current_data) if current_data else 0
            logger.info(f"📊 当前数据行数: {current_rows} 行")
        except Exception as read_error:
            logger.warning(f"⚠️ 无法读取当前数据行数: {read_error}")
            current_rows = 0

        # 2. 执行清空操作
        sheets_client.clear_values(target_range)
        logger.info(f"✅ 成功清空目标范围: {target_range}")

        # 3. 验证清空结果
        await asyncio.sleep(1)  # 等待API操作完成
        try:
            verification_data = sheets_client.read_sheet(sheets_client.spreadsheet_id, f"{sheet_name}!A1:J10")
            remaining_rows = len(verification_data) if verification_data else 0

            if remaining_rows == 0:
                logger.info(f"✅ 清空验证成功: 目标范围已完全清空")
            else:
                logger.warning(f"⚠️ 清空验证异常: 仍有 {remaining_rows} 行数据")
        except Exception as verify_error:
            logger.warning(f"⚠️ 清空验证失败: {verify_error}")

        # 4. 记录操作日志
        logger.info(f"📝 数据清空完成 - 据点: {facility_name}, 原数据: {current_rows} 行, 目标: {target_range}")

    except Exception as clear_error:
        logger.error(f"❌ 安全清空失败: {clear_error}")
        # 不抛出异常，允许继续执行写入操作
        logger.warning("⚠️ 清空失败，但继续执行数据写入（可能会覆盖现有数据）")


def get_monthly_data_summary(data: list) -> dict:
    """
    获取月度数据摘要信息

    Returns:
        dict: 包含数据统计信息的字典
    """
    if not data:
        return {"total_rows": 0, "date_rows": 0, "data_rows": 0, "days_covered": 0, "header_rows": 0}

    total_rows = len(data)
    date_rows = 0
    data_rows = 0
    header_rows = 0
    unique_dates = set()

    for i, row in enumerate(data):
        if row and len(row) > 0:
            first_cell = str(row[0])

            # 检查是否是表头行
            if i == 0 and ("選択" in first_cell or "利用者名" in first_cell):
                header_rows += 1
            # 检查是否是日期行
            elif "令和" in first_cell and "日" in first_cell:
                date_rows += 1
                # 提取日期信息
                import re
                date_match = re.search(r'(\d+)日', first_cell)
                if date_match:
                    unique_dates.add(date_match.group(1))
            # 其他为数据行
            else:
                data_rows += 1

    return {
        "total_rows": total_rows,
        "header_rows": header_rows,
        "date_rows": date_rows,
        "data_rows": data_rows,
        "days_covered": len(unique_dates)
    }


def create_daily_performance_header() -> list:
    """
    创建日报表的表头

    基于用户记忆中的表头信息：
    選択、利用者名、保険区分、提供時間、サービス内容、居宅介護支援事業所、予定、実績の確定状態、実績
    映射到 A-J 列，其中 J 列用于显示总记录数

    Returns:
        list: 包含10列的表头行
    """
    header = [
        "選択",           # A列：选择
        "利用者名",       # B列：用户姓名
        "保険区分",       # C列：保险区分
        "提供時間",       # D列：服务时间
        "サービス内容",   # E列：服务内容
        "居宅介護支援事業所", # F列：居宅介护支援事业所
        "予定",           # G列：预定
        "実績の確定状態", # H列：实绩确定状态
        "実績",           # I列：实绩
        "総記録数"        # J列：总记录数（用于显示每日总数）
    ]

    return header


async def process_dates_with_streaming_write(selector_executor: SelectorExecutor, date_list: list,
                                           facility_name: str, sheets_client: SheetsClient, target_range: str):
    """
    流式写入模式：逐日处理并写入，避免大数据集内存溢出

    适用于超大数据集（> 10,000行）
    """
    try:
        logger.info(f"🌊 开始流式写入模式处理 {len(date_list)} 天的数据")

        total_written_rows = 0
        sheet_name = target_range.split('!')[0]
        current_row = 2  # 从第2行开始写入（第1行是表头）

        for date_index, date_info in enumerate(date_list):
            try:
                # 处理单日数据
                daily_data = await process_single_date_data(
                    selector_executor, date_info, facility_name
                )

                if daily_data:
                    # 流式写入当日数据
                    start_row = current_row
                    end_row = current_row + len(daily_data) - 1
                    batch_range = f"{sheet_name}!A{start_row}:J{end_row}"

                    # 使用优雅的批量写入
                    await write_daily_data_to_sheets(sheets_client, batch_range, daily_data)

                    total_written_rows += len(daily_data)
                    current_row = end_row + 1

                    logger.info(f"✅ 日期 {date_info['formatted']} 流式写入完成: {len(daily_data)} 行，累计: {total_written_rows} 行")
                else:
                    logger.warning(f"⚠️ 日期 {date_info['formatted']} 没有数据")

                # 流式写入间隔等待，避免API限制
                if date_index < len(date_list) - 1:
                    await asyncio.sleep(0.5)

            except Exception as date_error:
                logger.error(f"❌ 日期 {date_info['formatted']} 流式处理失败: {date_error}")
                continue

        logger.info(f"✅ 流式写入完成: 据点 {facility_name}，总计 {total_written_rows} 行数据")

    except Exception as e:
        logger.error(f"❌ 流式写入模式失败: {e}")
        raise


def run(config: dict):
    """工作流入口函数"""
    asyncio.run(async_run(config))
